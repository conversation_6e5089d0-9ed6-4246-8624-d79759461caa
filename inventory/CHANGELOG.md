# Inventory 系统优化更新日志

## 主要更改

### 1. 数据库表结构更新

#### 新增字段：
- `item_sale_type` (INT): 道具销售类型，对应 `MainServer.InventoryItemSaleType` 枚举
- `summon_poke_name_id` (VARCHAR(50)): 召唤的宝可梦名称ID
- `seal_info` (JSONB): 封印的宝可梦信息，存储 `MainServer.InventorySealInfo` 数据

#### 字段修改：
- `special_coin` → `team_coin`: 字段名称更改，类型保持 INT
- `item_name`: 长度从 VARCHAR(30) 增加到 VARCHAR(50)

#### 索引优化：
- 添加了针对新字段的索引：
  - `idx_inventory_item_sale_type`
  - `idx_inventory_summon_poke_name_id` 
  - `idx_inventory_seal_info_poke_id` (JSON 字段索引)
- 重命名现有索引，添加 `inventory_` 前缀以避免冲突

#### 唯一约束更新：
- 原约束：`UNIQUE(tid, item_name, status)`
- 新约束：`UNIQUE(tid, item_name, summon_poke_name_id, status, item_sale_type, (seal_info->>'poke_id'))`
- 确保 `tid`, `item_name`, `summon_poke_name_id`, `status`, `item_sale_type`, `seal_info.poke_id` 确定一条唯一数据

### 2. 函数优化与合并

#### 新增核心函数：
- `UpsertItem()`: 合并了添加和更新功能的统一函数
- `CheckItemQuantityWithDetails()`: 支持新字段的道具数量检查，内部使用 `QueryItemsExtended()`
- `updateItemQuantityWithDetails()`: 支持新字段的道具数量更新
- `getItemById()`: 根据ID查询道具详细信息
- `deleteItemById()`: 根据ID删除道具记录
- `findSuitableItem()`: 统一的道具查找函数，复用现有查询函数

#### 函数签名更新：
- `SaleItem()`: 重新设计为 `(tid, itemName, count, price)` 参数，自动查找合适的道具进行上架
- `UnsaleItem()`: 重新设计为 `(tid, itemName, count)` 参数，自动查找合适的道具进行下架
- `RemoveItem()`: 增加了 `summonPokeNameId`, `sealInfo` 参数

#### 上架/下架逻辑优化：
- **上架道具**：
  - 使用 `findSuitableItem()` 查找 `itemSaleType` 为 `Normal` 或 `Team_Normal` 的普通状态道具
  - 优先选择数量最多的道具进行上架
  - 减少原道具数量，如果数量为0则删除记录
  - 使用 `UpsertItem()` 创建或合并上架状态的道具
- **下架道具**：
  - 使用 `findSuitableItem()` 查找上架状态的道具
  - 优先选择数量最多的道具进行下架
  - 减少上架道具数量，如果数量为0则删除记录
  - 使用 `UpsertItem()` 创建或合并普通状态的道具

#### 代码复用优化：
- 统一使用 `QueryItems()` 和 `QueryItemsExtended()` 进行查询
- `findSuitableItem()` 内部复用现有查询函数
- `CheckItemQuantityWithDetails()` 内部使用 `QueryItemsExtended()`
- `GetSaleableItems()` 和 `GetSaledItems()` 复用现有查询函数
- 删除了重复的查询逻辑，减少代码冗余

#### 向后兼容：
- 保留了原有的简化版本函数，内部调用新的详细版本函数
- `AddItem()`: 现在内部使用 `UpsertItem()`
- `CheckItemQuantity()`: 内部调用 `CheckItemQuantityWithDetails()`

### 3. 查询功能统一化

#### 统一查询架构：
- **删除了所有重复的查询函数**：`QueryItemsExtended()` 等
- **统一使用 `QueryItems(filter)`**：所有查询都通过一个函数完成
- **引入 `InventoryFilter` 结构**：使用 protobuf 定义的过滤条件

#### InventoryFilter 支持的查询条件：
- `id`: 道具ID（可选，如果指定则忽略其他条件，直接查询该道具）
- `tid`: 训练师ID（当未指定id时必填）
- `item_name`: 道具名称（可选）
- `status`: 道具状态（可选）
- `item_sale_type`: 销售类型（可选）
- `summon_poke_name_id`: 召唤宝可梦名称ID（可选）
- `seal_poke_id`: 封印宝可梦ID（可选）
- `update_ts`: 更新时间戳（可选）
- `min_quantity`: 最小数量（可选）
- `allowed_sale_types`: 允许的销售类型列表（可选）

#### 便捷函数（向后兼容）：
- `GetAllItems()`: 获取玩家所有道具
- `QueryItemsSimple()`: 简化的查询接口
- `GetSaleableItems()`: 获取可上架道具列表
- `GetSaledItems()`: 获取已上架道具列表
- `BatchUpsertItems()`: 批量添加或更新道具
- `GetItemTotalQuantity()`: 获取指定条件下的道具总数量
- `GetItemById()`: 通过ID查询单个道具
- `UpdateItemById()`: 通过ID修改并保存道具

### 4. Proto 字段映射

#### 数据库字段 → Proto 字段映射：
- `team_coin` → `TeamCoin` (int32)
- `item_sale_type` → `ItemSaleType` (InventoryItemSaleType)
- `summon_poke_name_id` → `SummonPokeNameId` (string)
- `seal_info` → `SealInfo` (*InventorySealInfo)

#### JSON 序列化处理：
- `seal_info` 字段在数据库中存储为 JSONB
- 读取时自动反序列化为 `MainServer.InventorySealInfo` 结构
- 写入时自动序列化为 JSON 字符串

### 5. RPC 接口更新

#### 更新的 RPC 调用：
- `SaleItem` RPC: 传递默认的 `ItemSaleType_Normal` 和空的封印信息
- `UnsaleItem` RPC: 传递空的召唤和封印信息

### 6. 数据完整性

#### 唯一性保证：
- 通过复合唯一约束确保数据的唯一性
- 支持同一道具在不同状态、销售类型、召唤宝可梦、封印信息下的多条记录

#### 默认值处理：
- `summon_poke_name_id` 默认为空字符串
- `seal_info` 默认为空 JSON 对象 `{}`
- 向后兼容的函数调用使用适当的默认值

## 使用示例

### 基本道具操作（向后兼容）：
```go
// 添加道具
err := AddItem(ctx, tx, trainerId, "potion", 10)

// 检查道具数量
hasEnough, err := CheckItemQuantity(ctx, tx, trainerId, "potion", 5)

// 移除道具（使用默认参数）
err := RemoveItem(ctx, tx, trainerId, "potion", 3, "", &MainServer.InventorySealInfo{})

// 获取所有道具（向后兼容）
items, err := GetAllItems(ctx, tx, trainerId, 0)
```

### 统一查询操作：
```go
// 使用 InventoryFilter 进行复杂查询
filter := &MainServer.InventoryFilter{
    Tid:              trainerId,
    ItemName:         "potion",
    Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
    ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Normal,
    SummonPokeNameId: "pikachu",
    SealPokeId:       12345,
    MinQuantity:      1,
    UpdateTs:         1234567890,
}
items, err := QueryItems(ctx, tx, filter)

// 通过 ID 查询单个道具
filter := &MainServer.InventoryFilter{
    Id: 12345,
}
items, err := QueryItems(ctx, tx, filter)
// 或者使用便捷函数
item, err := GetItemById(ctx, tx, 12345)

// 查询可上架道具
filter := &MainServer.InventoryFilter{
    Tid:    trainerId,
    Status: MainServer.InventoryStatus_InventoryStatus_Normal,
    AllowedSaleTypes: []MainServer.InventoryItemSaleType{
        MainServer.InventoryItemSaleType_ItemSaleType_Normal,
        MainServer.InventoryItemSaleType_ItemSaleType_Team_Normal,
    },
}
saleableItems, err := QueryItems(ctx, tx, filter)

// 简化查询（向后兼容）
items, err := QueryItemsSimple(ctx, tx, trainerId, "potion",
    MainServer.InventoryStatus_InventoryStatus_Normal, 0)
```

### 通过 ID 修改道具：
```go
// 方式1：使用便捷函数
err := UpdateItemById(ctx, tx, itemId, func(item *MainServer.Inventory) error {
    item.Quantity += 10  // 增加数量
    item.Price = 200     // 修改价格
    return nil
})

// 方式2：手动查询和保存
item, err := GetItemById(ctx, tx, itemId)
if err != nil {
    return err
}
item.Quantity += 10
item.Price = 200
err = UpsertItem(ctx, tx, item)
```

### 高级道具操作：
```go
// 创建带有召唤宝可梦信息的道具
inventory := &MainServer.Inventory{
    Tid:              trainerId,
    ItemName:         "summon_stone",
    Quantity:         1,
    Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
    ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Normal,
    SummonPokeNameId: "pikachu",
    SealInfo:         &MainServer.InventorySealInfo{},
}
err := UpsertItem(ctx, tx, inventory)
```

### 上架/下架操作：
```go
// 上架道具 - 自动查找合适的道具进行上架
err := SaleItem(ctx, tx, trainerId, "potion", 5, 100)

// 下架道具 - 自动查找合适的道具进行下架
err := UnsaleItem(ctx, tx, trainerId, "potion", 3)
```

## 注意事项

1. **数据库迁移**: 需要执行数据库迁移来添加新字段和索引
2. **Proto 更新**: 确保 proto 文件已更新并重新生成
3. **测试**: 现有测试需要更新以适应新的函数签名
4. **性能**: 新的复合索引可能会影响写入性能，但会提升查询性能
