// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Inventory.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InventoryType int32

const (
	InventoryType_inventory_nor       InventoryType = 0 //喷雾剂之类的
	InventoryType_inventory_poke      InventoryType = 1
	InventoryType_inventory_ball      InventoryType = 2  //精灵球
	InventoryType_inventory_battlesys InventoryType = 3  //mege & z
	InventoryType_inventory_healing   InventoryType = 4  //回复
	InventoryType_inventory_berry     InventoryType = 5  //果实
	InventoryType_inventory_box       InventoryType = 6  //礼盒之类的
	InventoryType_inventory_carry     InventoryType = 7  //携带道具
	InventoryType_inventory_cultivate InventoryType = 8  //进化，努力值，特性，性格，技能 极巨化培育 太晶化碎片
	InventoryType_inventory_special   InventoryType = 9  //特殊道具
	InventoryType_inventory_quest     InventoryType = 10 //任务道具
	// inventory_lock_trainer = 11; //锁定训练师道具
	InventoryType_inventory_summon InventoryType = 11 //召唤石
	InventoryType_inventory_seal   InventoryType = 12 //封印
)

// Enum value maps for InventoryType.
var (
	InventoryType_name = map[int32]string{
		0:  "inventory_nor",
		1:  "inventory_poke",
		2:  "inventory_ball",
		3:  "inventory_battlesys",
		4:  "inventory_healing",
		5:  "inventory_berry",
		6:  "inventory_box",
		7:  "inventory_carry",
		8:  "inventory_cultivate",
		9:  "inventory_special",
		10: "inventory_quest",
		11: "inventory_summon",
		12: "inventory_seal",
	}
	InventoryType_value = map[string]int32{
		"inventory_nor":       0,
		"inventory_poke":      1,
		"inventory_ball":      2,
		"inventory_battlesys": 3,
		"inventory_healing":   4,
		"inventory_berry":     5,
		"inventory_box":       6,
		"inventory_carry":     7,
		"inventory_cultivate": 8,
		"inventory_special":   9,
		"inventory_quest":     10,
		"inventory_summon":    11,
		"inventory_seal":      12,
	}
)

func (x InventoryType) Enum() *InventoryType {
	p := new(InventoryType)
	*p = x
	return p
}

func (x InventoryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InventoryType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Inventory_proto_enumTypes[0].Descriptor()
}

func (InventoryType) Type() protoreflect.EnumType {
	return &file_MainServer_Inventory_proto_enumTypes[0]
}

func (x InventoryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InventoryType.Descriptor instead.
func (InventoryType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{0}
}

// 库存状态枚举
type InventoryStatus int32

const (
	InventoryStatus_InventoryStatus_Unknown InventoryStatus = 0
	InventoryStatus_InventoryStatus_Normal  InventoryStatus = 1 // 普通状态
	InventoryStatus_InventoryStatus_Sale    InventoryStatus = 2 // 上架状态
)

// Enum value maps for InventoryStatus.
var (
	InventoryStatus_name = map[int32]string{
		0: "InventoryStatus_Unknown",
		1: "InventoryStatus_Normal",
		2: "InventoryStatus_Sale",
	}
	InventoryStatus_value = map[string]int32{
		"InventoryStatus_Unknown": 0,
		"InventoryStatus_Normal":  1,
		"InventoryStatus_Sale":    2,
	}
)

func (x InventoryStatus) Enum() *InventoryStatus {
	p := new(InventoryStatus)
	*p = x
	return p
}

func (x InventoryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InventoryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Inventory_proto_enumTypes[1].Descriptor()
}

func (InventoryStatus) Type() protoreflect.EnumType {
	return &file_MainServer_Inventory_proto_enumTypes[1]
}

func (x InventoryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InventoryStatus.Descriptor instead.
func (InventoryStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{1}
}

type InventoryItemSaleType int32

const (
	InventoryItemSaleType_ItemSaleType_Normal            InventoryItemSaleType = 0
	InventoryItemSaleType_ItemSaleType_Team_Normal       InventoryItemSaleType = 1
	InventoryItemSaleType_ItemSaleType_Trainer_Only      InventoryItemSaleType = 2
	InventoryItemSaleType_ItemSaleType_Team_Trainer_Only InventoryItemSaleType = 3
)

// Enum value maps for InventoryItemSaleType.
var (
	InventoryItemSaleType_name = map[int32]string{
		0: "ItemSaleType_Normal",
		1: "ItemSaleType_Team_Normal",
		2: "ItemSaleType_Trainer_Only",
		3: "ItemSaleType_Team_Trainer_Only",
	}
	InventoryItemSaleType_value = map[string]int32{
		"ItemSaleType_Normal":            0,
		"ItemSaleType_Team_Normal":       1,
		"ItemSaleType_Trainer_Only":      2,
		"ItemSaleType_Team_Trainer_Only": 3,
	}
)

func (x InventoryItemSaleType) Enum() *InventoryItemSaleType {
	p := new(InventoryItemSaleType)
	*p = x
	return p
}

func (x InventoryItemSaleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InventoryItemSaleType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Inventory_proto_enumTypes[2].Descriptor()
}

func (InventoryItemSaleType) Type() protoreflect.EnumType {
	return &file_MainServer_Inventory_proto_enumTypes[2]
}

func (x InventoryItemSaleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InventoryItemSaleType.Descriptor instead.
func (InventoryItemSaleType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{2}
}

type InventorySlot struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ItemId            string                 `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName          string                 `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity          int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Index             int32                  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	MaximumStack      int32                  `protobuf:"varint,5,opt,name=maximum_stack,json=maximumStack,proto3" json:"maximum_stack,omitempty"`
	TargetInventoryId int64                  `protobuf:"varint,6,opt,name=target_inventory_id,json=targetInventoryId,proto3" json:"target_inventory_id,omitempty"`
	Price             int64                  `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`                                //给商店用的
	Contribution      int64                  `protobuf:"varint,8,opt,name=contribution,proto3" json:"contribution,omitempty"`                  //给商店用的
	SpecialCoin       int64                  `protobuf:"varint,9,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"` //给商店用的
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InventorySlot) Reset() {
	*x = InventorySlot{}
	mi := &file_MainServer_Inventory_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InventorySlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventorySlot) ProtoMessage() {}

func (x *InventorySlot) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventorySlot.ProtoReflect.Descriptor instead.
func (*InventorySlot) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{0}
}

func (x *InventorySlot) GetItemId() string {
	if x != nil {
		return x.ItemId
	}
	return ""
}

func (x *InventorySlot) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *InventorySlot) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *InventorySlot) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *InventorySlot) GetMaximumStack() int32 {
	if x != nil {
		return x.MaximumStack
	}
	return 0
}

func (x *InventorySlot) GetTargetInventoryId() int64 {
	if x != nil {
		return x.TargetInventoryId
	}
	return 0
}

func (x *InventorySlot) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *InventorySlot) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *InventorySlot) GetSpecialCoin() int64 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

// 库存项目
type Inventory struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                         // 库存ID
	Tid              int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                       // 训练师ID
	ItemName         string                 `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`                              // 道具ID（道具名称）
	Quantity         int32                  `protobuf:"varint,4,opt,name=quantity,proto3" json:"quantity,omitempty"`                                             // 数量
	Price            int32                  `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`                                                   // 价格
	TeamCoin         int32                  `protobuf:"varint,6,opt,name=team_coin,json=teamCoin,proto3" json:"team_coin,omitempty"`                             // 团队贡献的价格
	TeamType         TrainerTeam            `protobuf:"varint,7,opt,name=team_type,json=teamType,proto3,enum=MainServer.TrainerTeam" json:"team_type,omitempty"` //团队类型 //比如召唤石是有团队类型的
	Status           InventoryStatus        `protobuf:"varint,8,opt,name=status,proto3,enum=MainServer.InventoryStatus" json:"status,omitempty"`                 // 状态
	ItemSaleType     InventoryItemSaleType  `protobuf:"varint,9,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.InventoryItemSaleType" json:"item_sale_type,omitempty"`
	SummonPokeNameId string                 `protobuf:"bytes,10,opt,name=summon_poke_name_id,json=summonPokeNameId,proto3" json:"summon_poke_name_id,omitempty"` //召唤的宝可梦
	SealPokeId       int64                  `protobuf:"varint,11,opt,name=seal_poke_id,json=sealPokeId,proto3" json:"seal_poke_id,omitempty"`                    //封印的宝可梦id
	SealInfo         *InventorySealInfo     `protobuf:"bytes,12,opt,name=seal_info,json=sealInfo,proto3" json:"seal_info,omitempty"`                             //封印的宝可梦信息
	CreateTs         int64                  `protobuf:"varint,13,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                            // 创建时间戳
	UpdateTs         int64                  `protobuf:"varint,14,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                            // 更新时间戳
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Inventory) Reset() {
	*x = Inventory{}
	mi := &file_MainServer_Inventory_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Inventory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Inventory) ProtoMessage() {}

func (x *Inventory) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Inventory.ProtoReflect.Descriptor instead.
func (*Inventory) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{1}
}

func (x *Inventory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Inventory) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Inventory) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *Inventory) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *Inventory) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Inventory) GetTeamCoin() int32 {
	if x != nil {
		return x.TeamCoin
	}
	return 0
}

func (x *Inventory) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *Inventory) GetStatus() InventoryStatus {
	if x != nil {
		return x.Status
	}
	return InventoryStatus_InventoryStatus_Unknown
}

func (x *Inventory) GetItemSaleType() InventoryItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return InventoryItemSaleType_ItemSaleType_Normal
}

func (x *Inventory) GetSummonPokeNameId() string {
	if x != nil {
		return x.SummonPokeNameId
	}
	return ""
}

func (x *Inventory) GetSealPokeId() int64 {
	if x != nil {
		return x.SealPokeId
	}
	return 0
}

func (x *Inventory) GetSealInfo() *InventorySealInfo {
	if x != nil {
		return x.SealInfo
	}
	return nil
}

func (x *Inventory) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Inventory) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type InventorySealInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	PokeNameId    string                 `protobuf:"bytes,2,opt,name=poke_name_id,json=pokeNameId,proto3" json:"poke_name_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InventorySealInfo) Reset() {
	*x = InventorySealInfo{}
	mi := &file_MainServer_Inventory_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InventorySealInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventorySealInfo) ProtoMessage() {}

func (x *InventorySealInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventorySealInfo.ProtoReflect.Descriptor instead.
func (*InventorySealInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{2}
}

func (x *InventorySealInfo) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *InventorySealInfo) GetPokeNameId() string {
	if x != nil {
		return x.PokeNameId
	}
	return ""
}

type InventoryFilter struct {
	state            protoimpl.MessageState  `protogen:"open.v1"`
	Id               int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid              int64                   `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                                                                   // 训练师ID（必填）
	ItemName         string                  `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`                                                                          // 道具名称（可选）
	Status           InventoryStatus         `protobuf:"varint,4,opt,name=status,proto3,enum=MainServer.InventoryStatus" json:"status,omitempty"`                                                             // 道具状态（可选）
	ItemSaleType     InventoryItemSaleType   `protobuf:"varint,5,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.InventoryItemSaleType" json:"item_sale_type,omitempty"`                     // 销售类型（可选）
	SummonPokeNameId string                  `protobuf:"bytes,6,opt,name=summon_poke_name_id,json=summonPokeNameId,proto3" json:"summon_poke_name_id,omitempty"`                                              // 召唤宝可梦名称ID（可选）
	SealPokeId       int64                   `protobuf:"varint,7,opt,name=seal_poke_id,json=sealPokeId,proto3" json:"seal_poke_id,omitempty"`                                                                 // 封印宝可梦ID（可选）
	UpdateTs         int64                   `protobuf:"varint,8,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                                                         // 更新时间戳（可选）
	MinQuantity      int32                   `protobuf:"varint,9,opt,name=min_quantity,json=minQuantity,proto3" json:"min_quantity,omitempty"`                                                                // 最小数量（可选）
	AllowedSaleTypes []InventoryItemSaleType `protobuf:"varint,10,rep,packed,name=allowed_sale_types,json=allowedSaleTypes,proto3,enum=MainServer.InventoryItemSaleType" json:"allowed_sale_types,omitempty"` // 允许的销售类型列表（可选）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *InventoryFilter) Reset() {
	*x = InventoryFilter{}
	mi := &file_MainServer_Inventory_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InventoryFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventoryFilter) ProtoMessage() {}

func (x *InventoryFilter) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventoryFilter.ProtoReflect.Descriptor instead.
func (*InventoryFilter) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{3}
}

func (x *InventoryFilter) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InventoryFilter) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *InventoryFilter) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *InventoryFilter) GetStatus() InventoryStatus {
	if x != nil {
		return x.Status
	}
	return InventoryStatus_InventoryStatus_Unknown
}

func (x *InventoryFilter) GetItemSaleType() InventoryItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return InventoryItemSaleType_ItemSaleType_Normal
}

func (x *InventoryFilter) GetSummonPokeNameId() string {
	if x != nil {
		return x.SummonPokeNameId
	}
	return ""
}

func (x *InventoryFilter) GetSealPokeId() int64 {
	if x != nil {
		return x.SealPokeId
	}
	return 0
}

func (x *InventoryFilter) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *InventoryFilter) GetMinQuantity() int32 {
	if x != nil {
		return x.MinQuantity
	}
	return 0
}

func (x *InventoryFilter) GetAllowedSaleTypes() []InventoryItemSaleType {
	if x != nil {
		return x.AllowedSaleTypes
	}
	return nil
}

// 使用道具请求参数
type UseItemParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"` // 道具ID（道具名称）
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                      // 使用数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseItemParam) Reset() {
	*x = UseItemParam{}
	mi := &file_MainServer_Inventory_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemParam) ProtoMessage() {}

func (x *UseItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemParam.ProtoReflect.Descriptor instead.
func (*UseItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{4}
}

func (x *UseItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 添加道具请求参数
type AddItemParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`                                                      // 道具ID（道具名称）
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                                                           // 添加数量
	ItemSaleType  InventoryItemSaleType  `protobuf:"varint,3,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.InventoryItemSaleType" json:"item_sale_type,omitempty"` // 销售类型
	TeamType      TrainerTeam            `protobuf:"varint,4,opt,name=team_type,json=teamType,proto3,enum=MainServer.TrainerTeam" json:"team_type,omitempty"`                         //团队类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddItemParam) Reset() {
	*x = AddItemParam{}
	mi := &file_MainServer_Inventory_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddItemParam) ProtoMessage() {}

func (x *AddItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddItemParam.ProtoReflect.Descriptor instead.
func (*AddItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{5}
}

func (x *AddItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *AddItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *AddItemParam) GetItemSaleType() InventoryItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return InventoryItemSaleType_ItemSaleType_Normal
}

func (x *AddItemParam) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

// 上架道具请求参数
type SaleItemParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`           // 道具ID（道具名称）
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                // 上架数量
	Price         int32                  `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`                                // 价格
	SpecialCoin   int32                  `protobuf:"varint,4,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"` // 特殊货币
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaleItemParam) Reset() {
	*x = SaleItemParam{}
	mi := &file_MainServer_Inventory_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaleItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleItemParam) ProtoMessage() {}

func (x *SaleItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleItemParam.ProtoReflect.Descriptor instead.
func (*SaleItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{6}
}

func (x *SaleItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *SaleItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SaleItemParam) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SaleItemParam) GetSpecialCoin() int32 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

// 下架道具请求参数
type UnsaleItemParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"` // 道具ID（道具名称）
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                      // 下架数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsaleItemParam) Reset() {
	*x = UnsaleItemParam{}
	mi := &file_MainServer_Inventory_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsaleItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsaleItemParam) ProtoMessage() {}

func (x *UnsaleItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsaleItemParam.ProtoReflect.Descriptor instead.
func (*UnsaleItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{7}
}

func (x *UnsaleItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UnsaleItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 查询道具请求参数
type QueryItemsParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`              // 道具ID（道具名称），空字符串表示查询所有道具
	Status        InventoryStatus        `protobuf:"varint,2,opt,name=status,proto3,enum=MainServer.InventoryStatus" json:"status,omitempty"` // 状态，0表示查询所有状态
	UpdateTs      int64                  `protobuf:"varint,3,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`             // 更新时间戳，大于0时只查询更新时间大于此值的记录
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryItemsParam) Reset() {
	*x = QueryItemsParam{}
	mi := &file_MainServer_Inventory_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryItemsParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryItemsParam) ProtoMessage() {}

func (x *QueryItemsParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryItemsParam.ProtoReflect.Descriptor instead.
func (*QueryItemsParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{8}
}

func (x *QueryItemsParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *QueryItemsParam) GetStatus() InventoryStatus {
	if x != nil {
		return x.Status
	}
	return InventoryStatus_InventoryStatus_Unknown
}

func (x *QueryItemsParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 使用道具响应
type UseItemResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseItemResponse) Reset() {
	*x = UseItemResponse{}
	mi := &file_MainServer_Inventory_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemResponse) ProtoMessage() {}

func (x *UseItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemResponse.ProtoReflect.Descriptor instead.
func (*UseItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{9}
}

func (x *UseItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UseItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 添加道具响应
type AddItemResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddItemResponse) Reset() {
	*x = AddItemResponse{}
	mi := &file_MainServer_Inventory_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddItemResponse) ProtoMessage() {}

func (x *AddItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddItemResponse.ProtoReflect.Descriptor instead.
func (*AddItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{10}
}

func (x *AddItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AddItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 上架道具响应
type SaleItemResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaleItemResponse) Reset() {
	*x = SaleItemResponse{}
	mi := &file_MainServer_Inventory_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaleItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleItemResponse) ProtoMessage() {}

func (x *SaleItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleItemResponse.ProtoReflect.Descriptor instead.
func (*SaleItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{11}
}

func (x *SaleItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SaleItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 下架道具响应
type UnsaleItemResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsaleItemResponse) Reset() {
	*x = UnsaleItemResponse{}
	mi := &file_MainServer_Inventory_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsaleItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsaleItemResponse) ProtoMessage() {}

func (x *UnsaleItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsaleItemResponse.ProtoReflect.Descriptor instead.
func (*UnsaleItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{12}
}

func (x *UnsaleItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UnsaleItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取所有道具请求参数
type GetAllItemsParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UpdateTs      int64                  `protobuf:"varint,1,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳，大于0时只查询更新时间大于此值的记录
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllItemsParam) Reset() {
	*x = GetAllItemsParam{}
	mi := &file_MainServer_Inventory_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllItemsParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllItemsParam) ProtoMessage() {}

func (x *GetAllItemsParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllItemsParam.ProtoReflect.Descriptor instead.
func (*GetAllItemsParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{13}
}

func (x *GetAllItemsParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 获取所有道具响应
type GetAllItemsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Items         []*Inventory           `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`      // 道具列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllItemsResponse) Reset() {
	*x = GetAllItemsResponse{}
	mi := &file_MainServer_Inventory_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllItemsResponse) ProtoMessage() {}

func (x *GetAllItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllItemsResponse.ProtoReflect.Descriptor instead.
func (*GetAllItemsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{14}
}

func (x *GetAllItemsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetAllItemsResponse) GetItems() []*Inventory {
	if x != nil {
		return x.Items
	}
	return nil
}

// 查询道具响应
type QueryItemsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Items         []*Inventory           `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`      // 道具列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryItemsResponse) Reset() {
	*x = QueryItemsResponse{}
	mi := &file_MainServer_Inventory_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryItemsResponse) ProtoMessage() {}

func (x *QueryItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryItemsResponse.ProtoReflect.Descriptor instead.
func (*QueryItemsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{15}
}

func (x *QueryItemsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *QueryItemsResponse) GetItems() []*Inventory {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_MainServer_Inventory_proto protoreflect.FileDescriptor

const file_MainServer_Inventory_proto_rawDesc = "" +
	"\n" +
	"\x1aMainServer/Inventory.proto\x12\n" +
	"MainServer\x1a\x1cMainServer/TrainerTeam.proto\"\xa9\x02\n" +
	"\rInventorySlot\x12\x17\n" +
	"\aitem_id\x18\x01 \x01(\tR\x06itemId\x12\x1b\n" +
	"\titem_name\x18\x02 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05index\x18\x04 \x01(\x05R\x05index\x12#\n" +
	"\rmaximum_stack\x18\x05 \x01(\x05R\fmaximumStack\x12.\n" +
	"\x13target_inventory_id\x18\x06 \x01(\x03R\x11targetInventoryId\x12\x14\n" +
	"\x05price\x18\a \x01(\x03R\x05price\x12\"\n" +
	"\fcontribution\x18\b \x01(\x03R\fcontribution\x12!\n" +
	"\fspecial_coin\x18\t \x01(\x03R\vspecialCoin\"\x94\x04\n" +
	"\tInventory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x1b\n" +
	"\titem_name\x18\x03 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x04 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\x05 \x01(\x05R\x05price\x12\x1b\n" +
	"\tteam_coin\x18\x06 \x01(\x05R\bteamCoin\x124\n" +
	"\tteam_type\x18\a \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\x123\n" +
	"\x06status\x18\b \x01(\x0e2\x1b.MainServer.InventoryStatusR\x06status\x12G\n" +
	"\x0eitem_sale_type\x18\t \x01(\x0e2!.MainServer.InventoryItemSaleTypeR\fitemSaleType\x12-\n" +
	"\x13summon_poke_name_id\x18\n" +
	" \x01(\tR\x10summonPokeNameId\x12 \n" +
	"\fseal_poke_id\x18\v \x01(\x03R\n" +
	"sealPokeId\x12:\n" +
	"\tseal_info\x18\f \x01(\v2\x1d.MainServer.InventorySealInfoR\bsealInfo\x12\x1b\n" +
	"\tcreate_ts\x18\r \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x0e \x01(\x03R\bupdateTs\"N\n" +
	"\x11InventorySealInfo\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12 \n" +
	"\fpoke_name_id\x18\x02 \x01(\tR\n" +
	"pokeNameId\"\xb0\x03\n" +
	"\x0fInventoryFilter\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x1b\n" +
	"\titem_name\x18\x03 \x01(\tR\bitemName\x123\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1b.MainServer.InventoryStatusR\x06status\x12G\n" +
	"\x0eitem_sale_type\x18\x05 \x01(\x0e2!.MainServer.InventoryItemSaleTypeR\fitemSaleType\x12-\n" +
	"\x13summon_poke_name_id\x18\x06 \x01(\tR\x10summonPokeNameId\x12 \n" +
	"\fseal_poke_id\x18\a \x01(\x03R\n" +
	"sealPokeId\x12\x1b\n" +
	"\tupdate_ts\x18\b \x01(\x03R\bupdateTs\x12!\n" +
	"\fmin_quantity\x18\t \x01(\x05R\vminQuantity\x12O\n" +
	"\x12allowed_sale_types\x18\n" +
	" \x03(\x0e2!.MainServer.InventoryItemSaleTypeR\x10allowedSaleTypes\"A\n" +
	"\fUseItemParam\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"\xc0\x01\n" +
	"\fAddItemParam\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12G\n" +
	"\x0eitem_sale_type\x18\x03 \x01(\x0e2!.MainServer.InventoryItemSaleTypeR\fitemSaleType\x124\n" +
	"\tteam_type\x18\x04 \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\"{\n" +
	"\rSaleItemParam\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x05R\x05price\x12!\n" +
	"\fspecial_coin\x18\x04 \x01(\x05R\vspecialCoin\"D\n" +
	"\x0fUnsaleItemParam\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"\x80\x01\n" +
	"\x0fQueryItemsParam\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x123\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1b.MainServer.InventoryStatusR\x06status\x12\x1b\n" +
	"\tupdate_ts\x18\x03 \x01(\x03R\bupdateTs\"E\n" +
	"\x0fUseItemResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"E\n" +
	"\x0fAddItemResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"F\n" +
	"\x10SaleItemResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"H\n" +
	"\x12UnsaleItemResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"/\n" +
	"\x10GetAllItemsParam\x12\x1b\n" +
	"\tupdate_ts\x18\x01 \x01(\x03R\bupdateTs\"\\\n" +
	"\x13GetAllItemsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12+\n" +
	"\x05items\x18\x02 \x03(\v2\x15.MainServer.InventoryR\x05items\"[\n" +
	"\x12QueryItemsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12+\n" +
	"\x05items\x18\x02 \x03(\v2\x15.MainServer.InventoryR\x05items*\xa6\x02\n" +
	"\rInventoryType\x12\x11\n" +
	"\rinventory_nor\x10\x00\x12\x12\n" +
	"\x0einventory_poke\x10\x01\x12\x12\n" +
	"\x0einventory_ball\x10\x02\x12\x17\n" +
	"\x13inventory_battlesys\x10\x03\x12\x15\n" +
	"\x11inventory_healing\x10\x04\x12\x13\n" +
	"\x0finventory_berry\x10\x05\x12\x11\n" +
	"\rinventory_box\x10\x06\x12\x13\n" +
	"\x0finventory_carry\x10\a\x12\x17\n" +
	"\x13inventory_cultivate\x10\b\x12\x15\n" +
	"\x11inventory_special\x10\t\x12\x13\n" +
	"\x0finventory_quest\x10\n" +
	"\x12\x14\n" +
	"\x10inventory_summon\x10\v\x12\x12\n" +
	"\x0einventory_seal\x10\f*d\n" +
	"\x0fInventoryStatus\x12\x1b\n" +
	"\x17InventoryStatus_Unknown\x10\x00\x12\x1a\n" +
	"\x16InventoryStatus_Normal\x10\x01\x12\x18\n" +
	"\x14InventoryStatus_Sale\x10\x02*\x91\x01\n" +
	"\x15InventoryItemSaleType\x12\x17\n" +
	"\x13ItemSaleType_Normal\x10\x00\x12\x1c\n" +
	"\x18ItemSaleType_Team_Normal\x10\x01\x12\x1d\n" +
	"\x19ItemSaleType_Trainer_Only\x10\x02\x12\"\n" +
	"\x1eItemSaleType_Team_Trainer_Only\x10\x03B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Inventory_proto_rawDescOnce sync.Once
	file_MainServer_Inventory_proto_rawDescData []byte
)

func file_MainServer_Inventory_proto_rawDescGZIP() []byte {
	file_MainServer_Inventory_proto_rawDescOnce.Do(func() {
		file_MainServer_Inventory_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Inventory_proto_rawDesc), len(file_MainServer_Inventory_proto_rawDesc)))
	})
	return file_MainServer_Inventory_proto_rawDescData
}

var file_MainServer_Inventory_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_MainServer_Inventory_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_MainServer_Inventory_proto_goTypes = []any{
	(InventoryType)(0),          // 0: MainServer.InventoryType
	(InventoryStatus)(0),        // 1: MainServer.InventoryStatus
	(InventoryItemSaleType)(0),  // 2: MainServer.InventoryItemSaleType
	(*InventorySlot)(nil),       // 3: MainServer.InventorySlot
	(*Inventory)(nil),           // 4: MainServer.Inventory
	(*InventorySealInfo)(nil),   // 5: MainServer.InventorySealInfo
	(*InventoryFilter)(nil),     // 6: MainServer.InventoryFilter
	(*UseItemParam)(nil),        // 7: MainServer.UseItemParam
	(*AddItemParam)(nil),        // 8: MainServer.AddItemParam
	(*SaleItemParam)(nil),       // 9: MainServer.SaleItemParam
	(*UnsaleItemParam)(nil),     // 10: MainServer.UnsaleItemParam
	(*QueryItemsParam)(nil),     // 11: MainServer.QueryItemsParam
	(*UseItemResponse)(nil),     // 12: MainServer.UseItemResponse
	(*AddItemResponse)(nil),     // 13: MainServer.AddItemResponse
	(*SaleItemResponse)(nil),    // 14: MainServer.SaleItemResponse
	(*UnsaleItemResponse)(nil),  // 15: MainServer.UnsaleItemResponse
	(*GetAllItemsParam)(nil),    // 16: MainServer.GetAllItemsParam
	(*GetAllItemsResponse)(nil), // 17: MainServer.GetAllItemsResponse
	(*QueryItemsResponse)(nil),  // 18: MainServer.QueryItemsResponse
	(TrainerTeam)(0),            // 19: MainServer.TrainerTeam
}
var file_MainServer_Inventory_proto_depIdxs = []int32{
	19, // 0: MainServer.Inventory.team_type:type_name -> MainServer.TrainerTeam
	1,  // 1: MainServer.Inventory.status:type_name -> MainServer.InventoryStatus
	2,  // 2: MainServer.Inventory.item_sale_type:type_name -> MainServer.InventoryItemSaleType
	5,  // 3: MainServer.Inventory.seal_info:type_name -> MainServer.InventorySealInfo
	1,  // 4: MainServer.InventoryFilter.status:type_name -> MainServer.InventoryStatus
	2,  // 5: MainServer.InventoryFilter.item_sale_type:type_name -> MainServer.InventoryItemSaleType
	2,  // 6: MainServer.InventoryFilter.allowed_sale_types:type_name -> MainServer.InventoryItemSaleType
	2,  // 7: MainServer.AddItemParam.item_sale_type:type_name -> MainServer.InventoryItemSaleType
	19, // 8: MainServer.AddItemParam.team_type:type_name -> MainServer.TrainerTeam
	1,  // 9: MainServer.QueryItemsParam.status:type_name -> MainServer.InventoryStatus
	4,  // 10: MainServer.GetAllItemsResponse.items:type_name -> MainServer.Inventory
	4,  // 11: MainServer.QueryItemsResponse.items:type_name -> MainServer.Inventory
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_MainServer_Inventory_proto_init() }
func file_MainServer_Inventory_proto_init() {
	if File_MainServer_Inventory_proto != nil {
		return
	}
	file_MainServer_TrainerTeam_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Inventory_proto_rawDesc), len(file_MainServer_Inventory_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Inventory_proto_goTypes,
		DependencyIndexes: file_MainServer_Inventory_proto_depIdxs,
		EnumInfos:         file_MainServer_Inventory_proto_enumTypes,
		MessageInfos:      file_MainServer_Inventory_proto_msgTypes,
	}.Build()
	File_MainServer_Inventory_proto = out.File
	file_MainServer_Inventory_proto_goTypes = nil
	file_MainServer_Inventory_proto_depIdxs = nil
}
