// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Role.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RoleDutyType int32

const (
	RoleDutyType_role_normal               RoleDutyType = 0
	RoleDutyType_role_battle               RoleDutyType = 1
	RoleDutyType_role_healing              RoleDutyType = 2
	RoleDutyType_role_store                RoleDutyType = 3
	RoleDutyType_role_train                RoleDutyType = 4  //列车
	RoleDutyType_role_ship                 RoleDutyType = 5  //轮船
	RoleDutyType_role_mirror_single_battle RoleDutyType = 6  //镜像单人的战斗
	RoleDutyType_role_mirror_party         RoleDutyType = 7  //镜像多人的战斗
	RoleDutyType_role_add_team             RoleDutyType = 8  //加入team的npc
	RoleDutyType_role_gym_leader           RoleDutyType = 9  // 道馆馆主
	RoleDutyType_role_gym_king             RoleDutyType = 10 // 天王
	RoleDutyType_role_gym_champion         RoleDutyType = 11 //冠军
)

// Enum value maps for RoleDutyType.
var (
	RoleDutyType_name = map[int32]string{
		0:  "role_normal",
		1:  "role_battle",
		2:  "role_healing",
		3:  "role_store",
		4:  "role_train",
		5:  "role_ship",
		6:  "role_mirror_single_battle",
		7:  "role_mirror_party",
		8:  "role_add_team",
		9:  "role_gym_leader",
		10: "role_gym_king",
		11: "role_gym_champion",
	}
	RoleDutyType_value = map[string]int32{
		"role_normal":               0,
		"role_battle":               1,
		"role_healing":              2,
		"role_store":                3,
		"role_train":                4,
		"role_ship":                 5,
		"role_mirror_single_battle": 6,
		"role_mirror_party":         7,
		"role_add_team":             8,
		"role_gym_leader":           9,
		"role_gym_king":             10,
		"role_gym_champion":         11,
	}
)

func (x RoleDutyType) Enum() *RoleDutyType {
	p := new(RoleDutyType)
	*p = x
	return p
}

func (x RoleDutyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleDutyType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Role_proto_enumTypes[0].Descriptor()
}

func (RoleDutyType) Type() protoreflect.EnumType {
	return &file_MainServer_Role_proto_enumTypes[0]
}

func (x RoleDutyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleDutyType.Descriptor instead.
func (RoleDutyType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{0}
}

type RoleStatusType int32

const (
	RoleStatusType_Role_Status_Normal RoleStatusType = 0
)

// Enum value maps for RoleStatusType.
var (
	RoleStatusType_name = map[int32]string{
		0: "Role_Status_Normal",
	}
	RoleStatusType_value = map[string]int32{
		"Role_Status_Normal": 0,
	}
)

func (x RoleStatusType) Enum() *RoleStatusType {
	p := new(RoleStatusType)
	*p = x
	return p
}

func (x RoleStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Role_proto_enumTypes[1].Descriptor()
}

func (RoleStatusType) Type() protoreflect.EnumType {
	return &file_MainServer_Role_proto_enumTypes[1]
}

func (x RoleStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleStatusType.Descriptor instead.
func (RoleStatusType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{1}
}

type NpcRoleConfig struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Name                  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Duty                  RoleDutyType           `protobuf:"varint,2,opt,name=duty,proto3,enum=MainServer.RoleDutyType" json:"duty,omitempty"`
	Cloth                 *TrainerCloth          `protobuf:"bytes,3,opt,name=cloth,proto3" json:"cloth,omitempty"`
	FollowPoke            *TrainerFollowPoke     `protobuf:"bytes,4,opt,name=follow_poke,json=followPoke,proto3" json:"follow_poke,omitempty"` //跟随的宝可梦
	Gender                Gender                 `protobuf:"varint,5,opt,name=gender,proto3,enum=MainServer.Gender" json:"gender,omitempty"`
	YarnName              string                 `protobuf:"bytes,6,opt,name=yarn_name,json=yarnName,proto3" json:"yarn_name,omitempty"`
	YarnTitle             string                 `protobuf:"bytes,7,opt,name=yarn_title,json=yarnTitle,proto3" json:"yarn_title,omitempty"`
	Team                  TrainerTeam            `protobuf:"varint,8,opt,name=team,proto3,enum=MainServer.TrainerTeam" json:"team,omitempty"`
	DefaultTransferPoints []string               `protobuf:"bytes,9,rep,name=default_transfer_points,json=defaultTransferPoints,proto3" json:"default_transfer_points,omitempty"` //默认传送点
	Status                RoleStatusType         `protobuf:"varint,10,opt,name=status,proto3,enum=MainServer.RoleStatusType" json:"status,omitempty"`
	IsHiden               bool                   `protobuf:"varint,11,opt,name=isHiden,proto3" json:"isHiden,omitempty"`
	PokeTeamIds           []string               `protobuf:"bytes,12,rep,name=poke_team_ids,json=pokeTeamIds,proto3" json:"poke_team_ids,omitempty"`                             //战斗poke队伍
	BattleRepeatInterval  int32                  `protobuf:"varint,13,opt,name=battle_repeat_interval,json=battleRepeatInterval,proto3" json:"battle_repeat_interval,omitempty"` //战斗重复间隔
	BattleWinRewardId     *QuestCompleteInfo     `protobuf:"bytes,14,opt,name=battle_win_reward_id,json=battleWinRewardId,proto3" json:"battle_win_reward_id,omitempty"`         //战斗胜利奖励
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *NpcRoleConfig) Reset() {
	*x = NpcRoleConfig{}
	mi := &file_MainServer_Role_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NpcRoleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NpcRoleConfig) ProtoMessage() {}

func (x *NpcRoleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Role_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NpcRoleConfig.ProtoReflect.Descriptor instead.
func (*NpcRoleConfig) Descriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{0}
}

func (x *NpcRoleConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NpcRoleConfig) GetDuty() RoleDutyType {
	if x != nil {
		return x.Duty
	}
	return RoleDutyType_role_normal
}

func (x *NpcRoleConfig) GetCloth() *TrainerCloth {
	if x != nil {
		return x.Cloth
	}
	return nil
}

func (x *NpcRoleConfig) GetFollowPoke() *TrainerFollowPoke {
	if x != nil {
		return x.FollowPoke
	}
	return nil
}

func (x *NpcRoleConfig) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderNull
}

func (x *NpcRoleConfig) GetYarnName() string {
	if x != nil {
		return x.YarnName
	}
	return ""
}

func (x *NpcRoleConfig) GetYarnTitle() string {
	if x != nil {
		return x.YarnTitle
	}
	return ""
}

func (x *NpcRoleConfig) GetTeam() TrainerTeam {
	if x != nil {
		return x.Team
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *NpcRoleConfig) GetDefaultTransferPoints() []string {
	if x != nil {
		return x.DefaultTransferPoints
	}
	return nil
}

func (x *NpcRoleConfig) GetStatus() RoleStatusType {
	if x != nil {
		return x.Status
	}
	return RoleStatusType_Role_Status_Normal
}

func (x *NpcRoleConfig) GetIsHiden() bool {
	if x != nil {
		return x.IsHiden
	}
	return false
}

func (x *NpcRoleConfig) GetPokeTeamIds() []string {
	if x != nil {
		return x.PokeTeamIds
	}
	return nil
}

func (x *NpcRoleConfig) GetBattleRepeatInterval() int32 {
	if x != nil {
		return x.BattleRepeatInterval
	}
	return 0
}

func (x *NpcRoleConfig) GetBattleWinRewardId() *QuestCompleteInfo {
	if x != nil {
		return x.BattleWinRewardId
	}
	return nil
}

type NpcRoleConfigList struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Configs       map[string]*NpcRoleConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NpcRoleConfigList) Reset() {
	*x = NpcRoleConfigList{}
	mi := &file_MainServer_Role_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NpcRoleConfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NpcRoleConfigList) ProtoMessage() {}

func (x *NpcRoleConfigList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Role_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NpcRoleConfigList.ProtoReflect.Descriptor instead.
func (*NpcRoleConfigList) Descriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{1}
}

func (x *NpcRoleConfigList) GetConfigs() map[string]*NpcRoleConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type BattleNpc struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Config        *NpcRoleConfig         `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	PokeTeam      *PokeTeamConfig        `protobuf:"bytes,2,opt,name=poke_team,json=pokeTeam,proto3" json:"poke_team,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleNpc) Reset() {
	*x = BattleNpc{}
	mi := &file_MainServer_Role_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleNpc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleNpc) ProtoMessage() {}

func (x *BattleNpc) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Role_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleNpc.ProtoReflect.Descriptor instead.
func (*BattleNpc) Descriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{2}
}

func (x *BattleNpc) GetConfig() *NpcRoleConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *BattleNpc) GetPokeTeam() *PokeTeamConfig {
	if x != nil {
		return x.PokeTeam
	}
	return nil
}

var File_MainServer_Role_proto protoreflect.FileDescriptor

const file_MainServer_Role_proto_rawDesc = "" +
	"\n" +
	"\x15MainServer/Role.proto\x12\n" +
	"MainServer\x1a\x18MainServer/Trainer.proto\x1a\x1dMainServer/TrainerCloth.proto\x1a\x1cMainServer/TrainerTeam.proto\x1a\x15MainServer/Poke.proto\x1a\x19MainServer/PokeTeam.proto\x1a\x1aMainServer/QuestInfo.proto\"\x86\x05\n" +
	"\rNpcRoleConfig\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12,\n" +
	"\x04duty\x18\x02 \x01(\x0e2\x18.MainServer.RoleDutyTypeR\x04duty\x12.\n" +
	"\x05cloth\x18\x03 \x01(\v2\x18.MainServer.TrainerClothR\x05cloth\x12>\n" +
	"\vfollow_poke\x18\x04 \x01(\v2\x1d.MainServer.TrainerFollowPokeR\n" +
	"followPoke\x12*\n" +
	"\x06gender\x18\x05 \x01(\x0e2\x12.MainServer.GenderR\x06gender\x12\x1b\n" +
	"\tyarn_name\x18\x06 \x01(\tR\byarnName\x12\x1d\n" +
	"\n" +
	"yarn_title\x18\a \x01(\tR\tyarnTitle\x12+\n" +
	"\x04team\x18\b \x01(\x0e2\x17.MainServer.TrainerTeamR\x04team\x126\n" +
	"\x17default_transfer_points\x18\t \x03(\tR\x15defaultTransferPoints\x122\n" +
	"\x06status\x18\n" +
	" \x01(\x0e2\x1a.MainServer.RoleStatusTypeR\x06status\x12\x18\n" +
	"\aisHiden\x18\v \x01(\bR\aisHiden\x12\"\n" +
	"\rpoke_team_ids\x18\f \x03(\tR\vpokeTeamIds\x124\n" +
	"\x16battle_repeat_interval\x18\r \x01(\x05R\x14battleRepeatInterval\x12N\n" +
	"\x14battle_win_reward_id\x18\x0e \x01(\v2\x1d.MainServer.QuestCompleteInfoR\x11battleWinRewardId\"\xb0\x01\n" +
	"\x11NpcRoleConfigList\x12D\n" +
	"\aconfigs\x18\x01 \x03(\v2*.MainServer.NpcRoleConfigList.ConfigsEntryR\aconfigs\x1aU\n" +
	"\fConfigsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12/\n" +
	"\x05value\x18\x02 \x01(\v2\x19.MainServer.NpcRoleConfigR\x05value:\x028\x01\"w\n" +
	"\tBattleNpc\x121\n" +
	"\x06config\x18\x01 \x01(\v2\x19.MainServer.NpcRoleConfigR\x06config\x127\n" +
	"\tpoke_team\x18\x02 \x01(\v2\x1a.MainServer.PokeTeamConfigR\bpokeTeam*\xf9\x01\n" +
	"\fRoleDutyType\x12\x0f\n" +
	"\vrole_normal\x10\x00\x12\x0f\n" +
	"\vrole_battle\x10\x01\x12\x10\n" +
	"\frole_healing\x10\x02\x12\x0e\n" +
	"\n" +
	"role_store\x10\x03\x12\x0e\n" +
	"\n" +
	"role_train\x10\x04\x12\r\n" +
	"\trole_ship\x10\x05\x12\x1d\n" +
	"\x19role_mirror_single_battle\x10\x06\x12\x15\n" +
	"\x11role_mirror_party\x10\a\x12\x11\n" +
	"\rrole_add_team\x10\b\x12\x13\n" +
	"\x0frole_gym_leader\x10\t\x12\x11\n" +
	"\rrole_gym_king\x10\n" +
	"\x12\x15\n" +
	"\x11role_gym_champion\x10\v*(\n" +
	"\x0eRoleStatusType\x12\x16\n" +
	"\x12Role_Status_Normal\x10\x00B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Role_proto_rawDescOnce sync.Once
	file_MainServer_Role_proto_rawDescData []byte
)

func file_MainServer_Role_proto_rawDescGZIP() []byte {
	file_MainServer_Role_proto_rawDescOnce.Do(func() {
		file_MainServer_Role_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Role_proto_rawDesc), len(file_MainServer_Role_proto_rawDesc)))
	})
	return file_MainServer_Role_proto_rawDescData
}

var file_MainServer_Role_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_Role_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_Role_proto_goTypes = []any{
	(RoleDutyType)(0),         // 0: MainServer.RoleDutyType
	(RoleStatusType)(0),       // 1: MainServer.RoleStatusType
	(*NpcRoleConfig)(nil),     // 2: MainServer.NpcRoleConfig
	(*NpcRoleConfigList)(nil), // 3: MainServer.NpcRoleConfigList
	(*BattleNpc)(nil),         // 4: MainServer.BattleNpc
	nil,                       // 5: MainServer.NpcRoleConfigList.ConfigsEntry
	(*TrainerCloth)(nil),      // 6: MainServer.TrainerCloth
	(*TrainerFollowPoke)(nil), // 7: MainServer.TrainerFollowPoke
	(Gender)(0),               // 8: MainServer.Gender
	(TrainerTeam)(0),          // 9: MainServer.TrainerTeam
	(*QuestCompleteInfo)(nil), // 10: MainServer.QuestCompleteInfo
	(*PokeTeamConfig)(nil),    // 11: MainServer.PokeTeamConfig
}
var file_MainServer_Role_proto_depIdxs = []int32{
	0,  // 0: MainServer.NpcRoleConfig.duty:type_name -> MainServer.RoleDutyType
	6,  // 1: MainServer.NpcRoleConfig.cloth:type_name -> MainServer.TrainerCloth
	7,  // 2: MainServer.NpcRoleConfig.follow_poke:type_name -> MainServer.TrainerFollowPoke
	8,  // 3: MainServer.NpcRoleConfig.gender:type_name -> MainServer.Gender
	9,  // 4: MainServer.NpcRoleConfig.team:type_name -> MainServer.TrainerTeam
	1,  // 5: MainServer.NpcRoleConfig.status:type_name -> MainServer.RoleStatusType
	10, // 6: MainServer.NpcRoleConfig.battle_win_reward_id:type_name -> MainServer.QuestCompleteInfo
	5,  // 7: MainServer.NpcRoleConfigList.configs:type_name -> MainServer.NpcRoleConfigList.ConfigsEntry
	2,  // 8: MainServer.BattleNpc.config:type_name -> MainServer.NpcRoleConfig
	11, // 9: MainServer.BattleNpc.poke_team:type_name -> MainServer.PokeTeamConfig
	2,  // 10: MainServer.NpcRoleConfigList.ConfigsEntry.value:type_name -> MainServer.NpcRoleConfig
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_MainServer_Role_proto_init() }
func file_MainServer_Role_proto_init() {
	if File_MainServer_Role_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_TrainerCloth_proto_init()
	file_MainServer_TrainerTeam_proto_init()
	file_MainServer_Poke_proto_init()
	file_MainServer_PokeTeam_proto_init()
	file_MainServer_QuestInfo_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Role_proto_rawDesc), len(file_MainServer_Role_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Role_proto_goTypes,
		DependencyIndexes: file_MainServer_Role_proto_depIdxs,
		EnumInfos:         file_MainServer_Role_proto_enumTypes,
		MessageInfos:      file_MainServer_Role_proto_msgTypes,
	}.Build()
	File_MainServer_Role_proto = out.File
	file_MainServer_Role_proto_goTypes = nil
	file_MainServer_Role_proto_depIdxs = nil
}
