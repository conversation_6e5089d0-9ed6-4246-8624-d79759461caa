package battle

import (
	"context"
	"database/sql"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

func BattleEnd(ctx context.Context, logger runtime.Logger, tx *sql.Tx, win *[]*MainServer.Poke, lose *[]*MainServer.Poke) error {
	for _, wpoke := range *win {
		for _, lpoke := range *lose {
			lpokemonData, exists := poke.GetPokemonInfo(lpoke.Name)
			if !exists {
				return runtime.NewError("无效的poke", 500)
			}
			addEfforts(wpoke, lpokemonData)
			if wpoke.Level < 100 {
				ex := poke.CalculateExperience(float64(lpokemonData.BaseExperience), float64(lpoke.Level), float64(wpoke.Level), 1, 1, 1, 1, 1, 1, 1)
				wpoke.Experience += int64(ex)
				configLevel(wpoke)
			}
		}
		if wpoke.Born != nil {
			wpoke.Born.IsBattle = true
		} else {
			wpoke.Born = &MainServer.BornInfo{
				IsBattle: true,
			}
		}
		poke.UpdatePokeData(ctx, tx, wpoke)
		//等级
	}
	for _, lpoke := range *lose {
		if lpoke.Born != nil {
			lpoke.Born.IsBattle = true
		} else {
			lpoke.Born = &MainServer.BornInfo{
				IsBattle: true,
			}
		}
		poke.UpdatePokeData(ctx, tx, lpoke)
	}
	return nil
}
func configLevel(wpoke *MainServer.Poke) error {
	wpokemonData, exists := poke.GetPokemonInfo(wpoke.Name)
	if !exists {
		return runtime.NewError("无效的wpoke: 找不到对应的宝可梦数据", 500)
	}

	// 获取成长率数据
	growthRate, exists := poke.GetPokemonGrowthRate(wpokemonData.GrowthRate)
	if !exists {
		return runtime.NewError("无效的growthRate: 找不到对应的成长率数据", 500)
	}
	for i := wpoke.Level; i < 100; i++ {
		if wpoke.Experience-int64(growthRate.Levels[i]) < 0 {
			wpoke.Level = i
			return nil
		}
	}
	return nil
}
func addEfforts(wpoke *MainServer.Poke, pokemonData *MainServer.PSPokemonData) {
	if wpoke.Evs.Hp+wpoke.Evs.Atk+wpoke.Evs.Def+wpoke.Evs.Spa+wpoke.Evs.Spd+wpoke.Evs.Spe >= 512 {
		return
	}
	wpoke.Evs.Hp += pokemonData.Efforts.Hp
	if wpoke.Evs.Hp > 255 {
		wpoke.Evs.Hp = 255
	}
	wpoke.Evs.Atk += pokemonData.Efforts.Atk
	if wpoke.Evs.Atk > 255 {
		wpoke.Evs.Atk = 255
	}
	wpoke.Evs.Def += pokemonData.Efforts.Def
	if wpoke.Evs.Def > 255 {
		wpoke.Evs.Def = 255
	}
	wpoke.Evs.Spa += pokemonData.Efforts.Spa
	if wpoke.Evs.Spa > 255 {
		wpoke.Evs.Spa = 255
	}
	wpoke.Evs.Spd += pokemonData.Efforts.Spd
	if wpoke.Evs.Spd > 255 {
		wpoke.Evs.Spd = 255
	}
	wpoke.Evs.Spe += pokemonData.Efforts.Spe
	if wpoke.Evs.Spe > 255 {
		wpoke.Evs.Spe = 255
	}
}
