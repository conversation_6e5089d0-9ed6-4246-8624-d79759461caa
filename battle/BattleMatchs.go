package battle

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/config"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	trainerpkg "go-nakama-poke/trainer"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type PokeMatchPresenceInfo struct {
	ai                      bool
	presence                runtime.Presence
	trainer                 *MainServer.Trainer
	leaveTurn               int //离开几个回合了 //第一个回合倒计时正常，第二个回合变成AI，无倒计时
	emptyTickCount          int64
	psPlayerKey             *string
	throughPointInfoRequest *MainServer.UpdateQuestThroughPointInfoRequest
	// waitBattleOutputMessages *MainServer.BattleOutputMessage
}

// PokeMatchState 结构体，用于维护比赛状态，包括玩家的准备状态和在线状态
type PokeMatchState struct {
	battleId    string
	presences   map[string]*PokeMatchPresenceInfo // 在线玩家 //用uid来表示因为离线之后有可能训练师数据在内存中删除了
	aiPresences []*PokeMatchPresenceInfo          //ai
	// tidPsPlayerMap    map[int64]string
	leavePresences    map[string]*PokeMatchPresenceInfo //匹配过程中离线的玩家
	audiencePresences map[string]*PokeMatchPresenceInfo //观看玩家
	// playersReady      map[string]bool                  // 玩家准备状态
	// playersTurnAction map[string]bool                  // 玩家每个回合的指令
	aiMessages  chan runtime.MatchData
	gameStarted bool // 比赛是否开始
	matchLabel  *MainServer.BattleMatchLabel
	// matchInfo         MainServer.BattleMatchInfo //比赛信息，包括玩家数量之类的
	// playerCount       int                   // 玩家总数
	emptyTickCount               int64                 // 最后一次活动的时间（tick 计数）
	battleType                   MainServer.BattleType //进行战斗的类型，NPC或者玩家或者野外
	audienceBattleOutputMessages *MainServer.BattleOutputMessageInfo
	// requiredTids   []string              //必须加入比赛的id
	chioces       map[int64]*MainServer.BattleDecisionChoiceInfo //记录当前回合玩家已经发送的指令
	battleStartTs int64                                          // 战斗开始时间戳
}

type PokeMatch struct{}

var PokeStandardMatch = "poke_standard_match"

const (
	KeyMatchOpen         string = "open"
	KeyMatchStyle        string = "matchStyle"
	KeyMatchStarted      string = "started"
	KeyMatchThreeTrainer string = "threeTrainerType"
)

// 初始化模块，注册比赛
// func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
//     logger.Info("Hello Multiplayer!")
//     err := initializer.RegisterMatch("standard_match", newMatch)

//     if err != nil {
//         logger.Error("[RegisterMatch] error: ", err.Error())
//         return err
//     }

//	    return nil
//	}
//
// 创建新比赛
func NewPokeMatch(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule) (m runtime.Match, err error) {
	return &PokeMatch{}, nil
}

func createCustomMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, battleType MainServer.BattleType, label *MainServer.BattleMatchLabel) (string, error) {
	_, err := FetchMostIdleServer(ctx, logger, nk)
	if err != nil {
		logger.Error("FetchMostIdleServer error: ", err.Error())
		return "", runtime.NewError("Create battle error.", 500)
	}
	// params, err := tool.GetJsonFromPayload(payload)
	// if err != nil {
	// 	logger.Error("Failed to parse payload: %v", err)
	// 	return "", runtime.NewError("Invalid payload", 400)
	// }
	labelJSON, err := protojson.MarshalOptions{
		UseEnumNumbers:  true,
		EmitUnpopulated: true, // 这个选项可以让零值字段也出现在 JSON 中
	}.Marshal(label)
	// proto.MarshalOptions{
	// 	EmitUnpopulated: true, // 这个选项可以让零值字段也出现在 JSON 中
	// }.Marshal(label)
	// jsonBytes, err := protojson.MarshalOptions{
	// 	EmitUnpopulated: true, // 这个选项可以让零值字段也出现在 JSON 中
	// }.Marshal(person)
	// labelJSON, err := json.Marshal(label)
	if err != nil {
		logger.WithField("error", err).Error("match init failed")
		labelJSON = []byte("{}")
	}
	params := map[string]interface{}{
		"battle_type": int(battleType),
		"label":       string(labelJSON),
	}
	// battleType, ok := data["battle_type"]
	// if !ok {
	// 	logger.Warn("Missing 'battle_type' field in payload")
	// 	return "", runtime.NewError("Missing 'rid' field", 400)
	// }

	//每一次新战斗就创建一个新的 match
	modulename := PokeStandardMatch // 与注册的模块名匹配
	// params := map[string]interface{}{
	// 	"battleType": battleType,
	// }

	matchID, err := nk.MatchCreate(ctx, modulename, params)
	if err != nil {
		logger.Error("无法创建比赛: %v", err)
		return "", err
	}

	logger.Info("比赛创建成功，Match ID: %v", matchID)
	return matchID, nil
}

// 比赛初始化
func (m *PokeMatch) MatchInit(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, params map[string]interface{}) (interface{}, int, string) {
	logger.Info("========================================= MatchInit %s", ctx.Value(runtime.RUNTIME_CTX_MATCH_ID).(string))
	rawBattleType, ok := params["battle_type"].(int)
	if !ok {
		logger.Error("Missing 'battle_type' field in match init parameter")
		return nil, 0, ""
	}
	label, ok := params["label"].(string)
	if !ok {
		logger.Error("Missing 'label' field in match init parameter")
		return nil, 0, ""
	}
	matchLabel := &MainServer.BattleMatchLabel{}
	err := json.Unmarshal([]byte(label), matchLabel)
	if err != nil {
		logger.Error("'label' json.Unmarshal err")
		return nil, 0, ""
	}
	battleType := MainServer.BattleType(rawBattleType)
	// matchInfo := &MainServer.BattleMatchInfo{}
	// // playerCount := 2
	// switch battleType {
	// case MainServer.BattleType_SingleWild, MainServer.BattleType_SingleNPC, MainServer.BattleType_SingleNPCAndDoublePokemon, MainServer.BattleType_SingleWildAndDoublePokemon:
	// 	matchInfo.Min = 1
	// 	matchInfo.Max = 1
	// case MainServer.BattleType_SinglePlayer:
	// 	matchInfo.Min = 2
	// 	matchInfo.Max = 2
	// case MainServer.BattleType_SinglePlayerAndDoublePokemon:
	// 	matchInfo.Min = 2
	// 	matchInfo.Max = 3
	// case MainServer.BattleType_DoubleNPC, MainServer.BattleType_DoubleWild, MainServer.BattleType_DoublePlayer:
	// 	matchInfo.Min = 3
	// 	matchInfo.Max = 4
	// }
	battleId := ctx.Value(runtime.RUNTIME_CTX_MATCH_ID).(string)
	state := &PokeMatchState{
		presences: make(map[string]*PokeMatchPresenceInfo),
		// tidPsPlayerMap:    make(map[int64]string),
		leavePresences:    make(map[string]*PokeMatchPresenceInfo),
		audiencePresences: make(map[string]*PokeMatchPresenceInfo),
		// playersReady:      make(map[string]bool), // 初始化玩家准备状态
		// playersTurnAction: make(map[string]bool),
		gameStarted: false,
		matchLabel:  matchLabel,
		// matchInfo:         matchInfo,
		// playerCount:       playerCount, // 假设比赛最多 4 名玩家 //观看的玩家另外设置
		battleType: MainServer.BattleType(battleType),
		aiMessages: make(chan runtime.MatchData, 1),
		audienceBattleOutputMessages: &MainServer.BattleOutputMessageInfo{
			BattleId:   battleId,
			BattleType: battleType,
			Messages:   make([]*MainServer.BattleOutputMessage, 0),
		},
		emptyTickCount: 0,
		chioces:        make(map[int64]*MainServer.BattleDecisionChoiceInfo),
		battleId:       battleId,
	}

	tickRate := 3
	// label := &MainServer.BattleMatchLabel{
	// 	Open:       false,
	// 	Started:    false,
	// 	PartyUpTwo: false,
	// }
	// labelJSON, err := json.Marshal(label)
	// if err != nil {
	// 	logger.WithField("error", err).Error("match init failed")
	// 	labelJSON = []byte("{}")
	// }

	return state, tickRate, label
}

// 尝试加入比赛
func (m *PokeMatch) MatchJoinAttempt(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presence runtime.Presence, metadata map[string]string) (interface{}, bool, string) {
	logger.Info("========================================= MatchJoinAttempt %s", ctx.Value(runtime.RUNTIME_CTX_MATCH_ID).(string))
	mState, _ := state.(*PokeMatchState)
	acceptUser := true
	battleInfo, exists := tool.GetGlobalBattleMap().Get(mState.battleId)
	if !exists {
		return nil, false, ""
	}
	if !mState.gameStarted {
		//用于判断 party里面的人加入战斗后是否人数太多了
		trainer := tool.GetActiveTrainerByUid(presence.GetUserId())
		partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
		joinCount := 1
		if exists {
			joinCount = len(partyInfo.Trainers)
		}
		if (int)(battleInfo.MatchLabel.Max) < len(mState.presences)+joinCount {
			acceptUser = false
		}
		// switch battleInfo.BattlePrepare.BattleType {
		// case MainServer.BattleType_SingleWild, MainServer.BattleType_SingleNPC, MainServer.BattleType_SingleNPCAndDoublePokemon, MainServer.BattleType_SingleWildAndDoublePokemon:
		// 	if len(mState.presences)+joinCount > 1 {
		// 		acceptUser = false
		// 	}
		// case MainServer.BattleType_DoubleWild, MainServer.BattleType_DoubleNPC:
		// 	if len(mState.presences)+joinCount < 2 {
		// 		acceptUser = false
		// 	}
		// case MainServer.BattleType_SinglePlayerAndDoublePokemon:
		// 	if battleInfo.BattlePrepare.BattleMatchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_accept {
		// 		if len(mState.presences)+joinCount < 2 {
		// 			acceptUser = false
		// 		}
		// 	}
		// case MainServer.BattleType_SinglePlayer:
		// 	if len(mState.presences)+joinCount != 2 {
		// 		acceptUser = false
		// 	}
		// case MainServer.BattleType_DoublePlayer:
		// 	if battleInfo.BattlePrepare.BattleMatchMaker == nil {
		// 		return nil, false, ""
		// 	}
		// 	if battleInfo.BattlePrepare.BattleMatchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_accept {
		// 		if len(mState.presences)+joinCount < 3 {
		// 			acceptUser = false
		// 		}
		// 	} else if battleInfo.BattlePrepare.BattleMatchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_only {
		// 		if len(mState.presences)+joinCount != 3 {
		// 			acceptUser = false
		// 		}
		// 	} else {
		// 		if len(mState.presences)+joinCount != 4 {
		// 			acceptUser = false
		// 		}
		// 	}
		// }
		// battleInfo.BattlePrepare.BattleMatchMaker
		// if mState.playerCount < len(mState.presences)+joinCount {
		// 	acceptUser = false
		// }
	}
	return state, acceptUser, ""
}
func createNewPokeMatchPresenceInfoBy(presence runtime.Presence) *PokeMatchPresenceInfo {
	// if presence.GetUserId() == aiUserId {
	// 	return &PokeMatchPresenceInfo{
	// 		ai:             true,
	// 		presence:       presence,
	// 		trainer:        nil,
	// 		leaveTurn:      0,
	// 		emptyTickCount: 0,
	// 		psPlayerKey:    nil,
	// 	}
	// }
	trainer := tool.GetActiveTrainerByUid(presence.GetUserId())
	if trainer != nil {
		return &PokeMatchPresenceInfo{
			ai:             false,
			presence:       presence,
			trainer:        trainer,
			leaveTurn:      0,
			emptyTickCount: 0,
			psPlayerKey:    nil,
		}
	}
	return nil
}

// 玩家加入比赛
func (m *PokeMatch) MatchJoin(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presences []runtime.Presence) interface{} {
	mState, _ := state.(*PokeMatchState)
	// if match 比赛开始 ，则加入的是观众
	if mState.gameStarted {
		for _, p := range presences {
			leavePresenceInfo, exists := mState.leavePresences[p.GetUserId()]
			if exists {
				mState.presences[p.GetUserId()] = leavePresenceInfo
				delete(mState.leavePresences, p.GetUserId())
				logger.Info("玩家 %v 已重新加入比赛", p.GetUserId())
			} else {
				presenceInfo := createNewPokeMatchPresenceInfoBy(p)
				if presenceInfo != nil {
					mState.audiencePresences[presenceInfo.trainer.Uid] = presenceInfo
				}
				logger.Info("玩家 %v 已加入观看比赛", p.GetUserId())
			}
		}
		toTrainerMessagByte, err := proto.Marshal(mState.audienceBattleOutputMessages)
		if err != nil {
			logger.Error("toTrainerMessage 数据转换错误", err)
			return nil
		}
		err = dispatcher.BroadcastMessage(int64(MainServer.BattleOutputMessageType_Prepare), toTrainerMessagByte, presences, nil, true)
		if err != nil {
			logger.Error("MatchLoop dispatcher.BroadcastMessage error", err)
			return nil
		}
		return mState
	}
	matchID := mState.battleId
	battleInfo, exists := tool.GetGlobalBattleMap().Get(matchID)
	if !exists {
		logger.Error("加入匹配的玩家没有找到相应的BattleInfo")
		return nil
	}
	//TODO通过partyInfo 判断是否允许该user加入

	// 将玩家加入 presences 和 playersReady
	for _, p := range presences {
		presenceInfo := createNewPokeMatchPresenceInfoBy(p)
		if presenceInfo == nil {
			logger.Info("一部分加入的玩家无法获取Trainer: " + matchID)
			continue
		}
		if !presenceInfo.ai {
			mState.presences[presenceInfo.trainer.Uid] = presenceInfo
			partyInfo, exists := tool.GetGlobalPartyMap().Get(presenceInfo.trainer.Id)
			//默认个人是没有群的
			if !exists {
				partyInfo = &MainServer.PartyInfo{
					Trainers: map[int64]*MainServer.Trainer{presenceInfo.trainer.Id: presenceInfo.trainer},
					PartyId:  presenceInfo.trainer.Uid,
					Leader:   presenceInfo.trainer,
				}
			}
			battleInfo.PartyInfos[partyInfo.PartyId] = partyInfo
		}

		// if p.GetUserId() == aiUserId {
		// 	// mState.playersReady[p.GetUserId()] = true
		// } else {
		// 	// mState.playersReady[p.GetUserId()] = false // 初始化为未准备
		// 	// trainer := tool.GetActiveTrainerByUid(p.GetUserId())
		// 	// if trainer == nil {
		// 	// 	logger.Error("加入匹配的玩家没有找到相应的Trainer")
		// 	// 	return mState
		// 	// }
		// 	partyInfo, exists := tool.GetGlobalPartyMap().Get(presenceInfo.trainer.Id)
		// 	//默认个人是没有群的
		// 	if !exists {
		// 		partyInfo := &tool.PartyInfo{
		// 			Trainers: map[int64]*MainServer.Trainer{trainer.Id: trainer},
		// 			PartyId:  trainer.Uid,
		// 			Leader:   trainer,
		// 		}
		// 		battleInfo.PartyInfos[trainer.Uid] = partyInfo
		// 	} else {
		// 		battleInfo.PartyInfos[partyInfo.PartyId] = partyInfo
		// 	}
		// }
		// mState.playersTurnAction[p.GetUserId()] = false
		logger.Info("玩家 %v 已加入比赛", p.GetUserId())

	}
	serverID, ok := MatchShouldStartLoadBattle(ctx, logger, nk, mState, battleInfo)
	if !mState.gameStarted && ok { //可以进入战斗
		mState.gameStarted = true
		mState.matchLabel.Started = true
		mState.battleStartTs = time.Now().UnixMilli() // 记录战斗开始时间
		battleInfo.BattleServerId = serverID
		battleLoadMessage, err := loadBattle(ctx, logger, db, nk, battleInfo)
		if err != nil {
			logger.Error("loadBattle error", err)
			return nil
		}
		for _, message := range battleLoadMessage.toTrainerMessage.Messages {
			//用来汇集所有的psPlayerKey
			for _, trainerInfo := range message.TrainerInfos {
				if trainerInfo.IsAi {
					continue
				}
				trainerInfo.Trainer.SessionInfo.MatchId = matchID
				presenceInfo, exist := mState.presences[trainerInfo.Trainer.Uid]
				if !exist {
					logger.Error("没有找到对应的Presence")
					return nil
				}
				presenceInfo.psPlayerKey = &trainerInfo.PsPlayerKey
				// mState.tidPsPlayerMap[trainerInfo.Trainer.Id] = trainerInfo.PsPlayerKey
			}
			message.MessageType = MainServer.BattleOutputMessageType_Prepare
			message.Ts = time.Now().Unix() //不需要太精确
		}
		mPresences := make([]runtime.Presence, 0, len(mState.presences))
		for _, v := range mState.presences {
			mPresences = append(mPresences, v.presence)
		}
		toTrainerMessage, err := proto.Marshal(battleLoadMessage.toTrainerMessage)
		if err != nil {
			logger.Error("toTrainerMessage 数据转换错误", err)
			return nil
		}
		err = dispatcher.BroadcastMessage(int64(MainServer.BattleOutputMessageType_Prepare), toTrainerMessage, mPresences, nil, true)
		if err != nil {
			logger.Error("toTrainerMessage 数据发送错误", err)
			return nil
		}
		//将准备信息添加到观看消息中
		mState.audienceBattleOutputMessages.Messages = append(mState.audienceBattleOutputMessages.Messages, battleLoadMessage.toTrainerMessage.Messages...)
		battleLoadMessage.toBattleServerMessage.BattleType = mState.battleType
		toBattleServerMessage, err := tool.ProtoToBase64(battleLoadMessage.toBattleServerMessage)
		if err != nil {
			logger.Error("toBattleServerMessage 数据转换错误", err)
			return nil
		}
		// nk.StreamSend(config.BattleStreamMode, "", "", "label", toBattleServerMessage, nil, true)
		err = nk.NotificationSend(ctx, serverID, "b_s_w", map[string]interface{}{"data": toBattleServerMessage}, int(MainServer.ServerNotificationType_ServerNotificationType_BattlePrepare), serverID, false)
		if err != nil {
			logger.Error("toBattleServerMessage 数据发送错误", err)
			return nil
		}
	}

	return mState
}

// return 空闲的battle server， ok
func MatchShouldStartLoadBattle(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, state *PokeMatchState, battleInfo *tool.BattleInfo) (string, bool) {
	switch battleInfo.BattlePrepare.BattleType {
	case MainServer.BattleType_BattleType_Unknow,
		MainServer.BattleType_AI_2,
		MainServer.BattleType_AI_4,
		MainServer.BattleType_Msg:
		return "", false
	case MainServer.BattleType_SinglePlayer,
		MainServer.BattleType_SinglePlayerAndDoublePokemon:
		if len(battleInfo.PartyInfos) != 2 {
			return "", false
		}
	case MainServer.BattleType_DoublePlayer:
		if len(battleInfo.PartyInfos) != 2 {
			return "", false
		}
	case MainServer.BattleType_DoubleNPC,
		MainServer.BattleType_SingleNPC,
		MainServer.BattleType_SingleNPCAndDoublePokemon,
		MainServer.BattleType_SingleWild,
		MainServer.BattleType_DoubleWild,
		MainServer.BattleType_SingleWildAndDoublePokemon:
		break
	}
	// if battleInfo.BattlePrepare.BattleType
	for _, info := range battleInfo.PartyInfos {
		for _, trainer := range info.Trainers {
			_, exists := state.presences[trainer.Uid]
			if !exists {
				return "", false
			}
		}
	}
	serverID, err := AssignServerToMatch(ctx, logger, nk, battleInfo.BattleID)
	if err != nil {
		logger.Error("AssignServerToMatch 获取空闲战斗服务器失败", err)
		return "", false
	}
	//是否有强制加入战斗的id，可能是pk或者抢
	// for _, tid := range battleInfo.BattlePrepare.
	return serverID, true
}

// 玩家离开比赛
func (m *PokeMatch) MatchLeave(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presences []runtime.Presence) interface{} {
	mState, _ := state.(*PokeMatchState)
	leaveMatch(mState, presences, logger)
	return mState
}

func leaveMatch(mState *PokeMatchState, presences []runtime.Presence, logger runtime.Logger) *PokeMatchState {
	// 删除离开的玩家
	for _, p := range presences {
		info, exists := mState.presences[p.GetUserId()]
		if exists {
			mState.leavePresences[p.GetUserId()] = info
		}
		// delete(mState.presences, p.GetUserId()) //不删除，只是通过leavePresences记录
		delete(mState.audiencePresences, p.GetUserId())
		logger.Info("玩家 %v 已离开比赛", p.GetUserId())
	}
	//TODO判断最后一个玩家是不是AI如果是AI也停止比赛
	presencesCount := len(mState.presences)
	// 如果所有玩家都离开了比赛，终止比赛
	if presencesCount == 0 || presencesCount == len(mState.aiPresences) {
		logger.Info("所有玩家已离线，终止比赛")
		return nil // 返回 nil 表示比赛结束
	}
	return mState
}

// 比赛主循环，处理玩家准备消息并广播比赛状态
func (m *PokeMatch) MatchLoop(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, messages []runtime.MatchData) interface{} {
	logger.Info("MatchLoop 在循环")
	mState, _ := state.(*PokeMatchState)
	mState.emptyTickCount++
	if mState.emptyTickCount > config.MaxMatchEmptyTickCount {
		logger.Info("MatchLoop 超过最大空帧次数，终止比赛")
		return nil
	}
	if !mState.gameStarted {
		return mState
	}
	matchID := ctx.Value(runtime.RUNTIME_CTX_MATCH_ID).(string)
	battleInfo, exists := tool.GetGlobalBattleMap().Get(matchID)
	if !exists {
		return nil
	}
	select {
	case msg, ok := <-battleInfo.BattleServerMessages:
		if !ok {
			return mState
		}
		if msg.Permission == MainServer.BattleServerMessagePermission_Audience {
			message := &MainServer.BattleOutputMessage{
				MessageType:   MainServer.BattleOutputMessageType_Normal,
				BattleContent: msg.Content,
			}
			mState.audienceBattleOutputMessages.Messages = append(mState.audienceBattleOutputMessages.Messages, message)
			return mState
		}
		logger.Info("收到消息 %v 来自战斗服务器", msg.String())
		var presenceInfo *PokeMatchPresenceInfo
		trainer := tool.GetActiveTrainerByTid(msg.Tid)
		if trainer != nil {
			presenceInfo, exists = mState.presences[trainer.Uid]
			if !exists {
				presenceInfo = nil
			}
		}
		if presenceInfo != nil {
			message := &MainServer.BattleOutputMessage{
				PsPlayerKey:   *presenceInfo.psPlayerKey,
				MessageType:   MainServer.BattleOutputMessageType_Normal,
				BattleContent: msg.Content,
			}
			toTrainerMessage := &MainServer.BattleOutputMessageInfo{
				BattleId:   msg.BattleId,
				BattleType: mState.battleType,
				Messages:   []*MainServer.BattleOutputMessage{message},
			}
			toTrainerMessagByte, err := proto.Marshal(toTrainerMessage)
			if err != nil {
				logger.Error("toTrainerMessage 数据转换错误", err)
				return nil
			}
			fmt.Println("Received:", msg)
			fmt.Println("Trainer:", trainer)
			err = dispatcher.BroadcastMessage(int64(MainServer.BattleOutputMessageType_Normal), toTrainerMessagByte, []runtime.Presence{presenceInfo.presence}, nil, true)
			if err != nil {
				logger.Error("MatchLoop dispatcher.BroadcastMessage error", err)
				return nil
			}
		}

		// trainer := tool.GetActiveTrainerByTid(msg.Tid)
		// if trainer != nil {
		// 	// 处理接收到的消息
		// 	fmt.Println("Received:", msg)
		// 	fmt.Println("Trainer:", trainer)
		// 	err = dispatcher.BroadcastMessage(int64(MainServer.BattleOutputMessageType_Normal), toTrainerMessagByte, []runtime.Presence{mState.presences[trainer.Uid]}, nil, true)
		// 	if err != nil {
		// 		logger.Error("MatchLoop dispatcher.BroadcastMessage error", err)
		// 		return nil
		// 	}
		// }
		mState.emptyTickCount = 0
		if len(msg.WinInfos) > 0 { //关闭match
			// 处理战斗结束时的经验增长
			// 直接使用 battleInfo 中已有的宝可梦数据
			if battleInfo != nil && len(battleInfo.BattlePokes) > 0 {
				var winPokes []*MainServer.Poke
				var losePokes []*MainServer.Poke

				// 根据WinInfos确定胜利与失败的训练师ID
				winnerIds := make(map[int64]bool)
				for _, winInfo := range msg.WinInfos {
					for _, winTid := range winInfo.WinTids {
						winnerIds[winTid] = true
					}
				}

				// 根据trainerId分类宝可梦到胜败队伍
				for trainerId, pokeMap := range battleInfo.BattlePokes {
					// 检查该训练师是否在胜利者名单中
					if _, isWinner := winnerIds[trainerId]; isWinner {
						// 将该训练师的所有宝可梦添加到胜利队伍
						for _, poke := range pokeMap {
							winPokes = append(winPokes, poke)
							logger.Info("胜利方宝可梦: %s, 等级: %d", poke.Name, poke.Level)
						}
					} else {
						// 将该训练师的所有宝可梦添加到失败队伍
						for _, poke := range pokeMap {
							losePokes = append(losePokes, poke)
							logger.Info("失败方宝可梦: %s, 等级: %d", poke.Name, poke.Level)
						}
					}
				}

				// 确保有宝可梦数据再调用BattleEnd
				if len(winPokes) > 0 && len(losePokes) > 0 {
					// 使用事务处理
					tx, err := db.Begin()
					if err != nil {
						logger.Error("创建事务失败: %v", err)
					} else {
						err = BattleEnd(ctx, logger, tx, &winPokes, &losePokes)
						if err != nil {
							tx.Rollback()
							logger.Error("BattleEnd处理失败: %v", err)
						} else {
							//假如这场战斗是任务触发的，那么要完成这个节点，并且记录到任务中
							if presenceInfo.throughPointInfoRequest != nil && presenceInfo.throughPointInfoRequest.QuestId > 0 && presenceInfo.throughPointInfoRequest.ThroughPointInfo != nil {
								err = trainerpkg.UpdateYarnTitleQuestProgress(ctx, logger, tx, trainer.Id, presenceInfo.throughPointInfoRequest.QuestId, presenceInfo.throughPointInfoRequest.ThroughPointInfo)
								if err != nil {
									tx.Rollback()
									logger.Error("UpdateYarnTitleQuestProgress处理失败: %v", err)
								}
							}
							// 记录战斗数据
							err = RecordBattleData(ctx, logger, tx, mState.battleId, mState.battleType, winPokes, losePokes, mState.battleStartTs, "", "")
							if err != nil {
								tx.Rollback()
								logger.Error("RecordBattleData处理失败: %v", err)
							} else {
								tx.Commit()
								logger.Info("战斗结算完成，宝可梦经验已更新，战斗数据已记录")
							}
						}
					}
				} else {
					logger.Warn("无法获取到足够的宝可梦数据进行战斗经验结算")
				}
			} else {
				logger.Warn("无法获取到 BattleInfo 或者 BattlePokes 数据为空")
			}

			trainer.SessionInfo.MatchId = ""
			delete(mState.presences, trainer.Uid)
			if len(mState.presences) == 0 {
				logger.Info("比赛结束，终止比赛")
				return nil // 返回 nil 表示比赛结束
			}
			return mState
		}
		if presenceInfo == nil {
			return mState
		}
	default:

	}
	// for msg := range battleInfo.BattleServerMessages {
	// 	logger.Info("收到消息 %v 来自战斗服务器", msg.Content)
	// 	toTrainerMessage := &MainServer.BattleOutputMessage{
	// 		BattleId:      msg.BattleId,
	// 		BattleType:    mState.battleType,
	// 		MessageType:   MainServer.BattleOutputMessageType_Normal,
	// 		BattleContent: msg.Content,
	// 	}
	// 	toTrainerMessagByte, err := proto.Marshal(toTrainerMessage)
	// 	if err != nil {
	// 		logger.Error("toTrainerMessage 数据转换错误", err)
	// 		return nil
	// 	}
	// 	trainer := tool.GetActiveTrainerByTid(msg.Tid)
	// 	if trainer != nil {
	// 		// 处理接收到的消息
	// 		fmt.Println("Received:", msg)
	// 		fmt.Println("Trainer:", trainer)
	// 		err = dispatcher.BroadcastMessage(int64(MainServer.BattleOutputMessageType_Normal), toTrainerMessagByte, []runtime.Presence{mState.presences[trainer.Uid]}, nil, true)
	// 		if err != nil {
	// 			logger.Error("MatchLoop dispatcher.BrochoiceadcastMessage error", err)
	// 			return nil
	// 		}
	// 	}
	// }
	forceSend := false
	//得判断必须是参战人员才能发送指令 //组合成一条信息 //判断是不是所有人指令都已经准备好
	for _, msg := range messages {
		presenceInfo, exists := mState.presences[msg.GetUserId()]
		if exists {
			// trainer := tool.GetActiveTrainerByUid(msg.GetUserId())
			// if trainer != nil {
			clintDecisionMessage := &MainServer.BattleClientDecisionMessage{}
			err := proto.Unmarshal(msg.GetData(), clintDecisionMessage)
			if err != nil {
				//直接发送使用ai
				return nil
			}
			logger.Info("收到消息 %v 来自玩家 %v", clintDecisionMessage.String(), msg.GetUserId())
			if clintDecisionMessage.ThroughPointInfoRequest != nil &&
				clintDecisionMessage.ThroughPointInfoRequest.QuestId > 0 &&
				clintDecisionMessage.ThroughPointInfoRequest.ThroughPointInfo != nil {
				presenceInfo.throughPointInfoRequest = clintDecisionMessage.ThroughPointInfoRequest
			}
			decisionChoiceInfo := &MainServer.BattleDecisionChoiceInfo{
				Tid:      presenceInfo.trainer.Id,
				Done:     true,
				IsGiveup: false,
			}
			// choiceContent := "defaut"
			choiceArray := []string{}
			for _, clientChoice := range clintDecisionMessage.Choices {
				re := regexp.MustCompile("[^a-zA-Z0-9]")
				clientChoice.Choice = re.ReplaceAllString(clientChoice.Choice, "")
				switch clientChoice.ChoiceType {
				case MainServer.BattleChoiceType_Choice_Switch, MainServer.BattleChoiceType_Choice_ForceSwitch:
					forceSend = clientChoice.ChoiceType == MainServer.BattleChoiceType_Choice_ForceSwitch
					choiceArray = append(choiceArray, fmt.Sprintf("switch %d", clientChoice.TargetSlot))
				case MainServer.BattleChoiceType_Choice_Move:
					//验证move是否合法 //不验证//战斗系统会报错 //mega还需要
					choiceArray = append(choiceArray, fmt.Sprintf("move %s", clientChoice.Choice))
					// pokes, exists := battleInfo.BattlePokes[trainer.Id]
					// if exists {
					// 	for _, poke := range pokes {
					// 		for _, move := range poke.Moves {
					// 			if move.Name == clientChoice.Choice {

					// 			}
					// 		}
					// 	}
					// }
					// choiceContent = fmt.FormatString("%d move %s", clientChoice.Slot, clientChoice.Choice)
				case MainServer.BattleChoiceType_Choice_Titem:
					//验证titem是否合法
					choiceArray = append(choiceArray, fmt.Sprintf("titem %s %d", clientChoice.Choice, clientChoice.TargetSlot))
				case MainServer.BattleChoiceType_Choice_TeamAck:
					choiceArray = append(choiceArray, fmt.Sprintf("team %s", clientChoice.Choice))
					var presences []runtime.Presence
					for _, presence := range mState.presences {
						//自己不需要发送
						if presence.presence.GetUserId() != msg.GetUserId() { //&& presence.presence.GetUserId() != aiUserId
							presences = append(presences, presence.presence)
						}
					}
					message := &MainServer.BattleOutputMessage{
						PsPlayerKey:   *presenceInfo.psPlayerKey,
						MessageType:   MainServer.BattleOutputMessageType_TeamAck,
						BattleContent: strconv.FormatInt(presenceInfo.trainer.Id, 10),
					}
					outMessage := &MainServer.BattleOutputMessageInfo{
						BattleId:   mState.battleId,
						BattleType: mState.battleType,
						Messages:   []*MainServer.BattleOutputMessage{message},
					}
					toTrainerMessagByte, err := proto.Marshal(outMessage)
					if err != nil {
						logger.Error("outMessage -> toTrainerMessage 数据转换错误", err)
						return nil
					}
					dispatcher.BroadcastMessage(int64(MainServer.BattleOutputMessageType_TeamAck), toTrainerMessagByte, presences, presenceInfo.presence, true)
					// choiceContent = fmt.FormatString("%d titem %s", clientChoice.Slot, clientChoice.Choice)
				case MainServer.BattleChoiceType_Choice_GiveUp:
					//如果是野外或者npc那就直接让血归0 //然后发送退出 //如果是双人 //那么需要两个人都设置 //不然就变成AI
					//或者下次登录之后直接血变成0

					decisionChoiceInfo.IsGiveup = true
				case MainServer.BattleChoiceType_Choice_Run:
					//是否加入逃跑的功能
					// choiceContent = fmt.FormatString("%d exit %s", clientChoice.Slot, clientChoice.Choice)
				}
				// decisionChoiceInfo.Choice = chioce
				// mState.chioces
			}
			decisionChoiceInfo.Choice = strings.Join(choiceArray, ",")
			// chioce := &MainServer.BattleDecisionChoiceInfo{
			// 	Tid:    trainer.Id,
			// 	Choice: strings.Join(choiceArray, ","),
			// }
			mState.chioces[presenceInfo.trainer.Id] = decisionChoiceInfo
			// switch decisionMessage.Chioces
			// chioce := &MainServer.BattleDecisionChoiceInfo{
			// 	Tid: trainer.Id,
			// 	Choice:
			// }
			// mState.chioces[]
			// }
		}
	}
	// _, haveAi := mState.presences[aiUserId]
	// aiCount := 0
	// if haveAi {
	// 	aiCount++
	// }
	if forceSend || len(mState.presences) > 0 && len(mState.chioces) >= len(mState.presences) {
		decisionMessage := &MainServer.BattleDecisionMessage{
			BattleId:     battleInfo.BattleID,
			BattleType:   battleInfo.BattlePrepare.BattleType,
			DecisionType: MainServer.BattleDecisionType_Choice,
			Choices:      mState.chioces,
		}
		toBattleServerMessage, err := tool.ProtoToBase64(decisionMessage)
		if err != nil {
			logger.Error("toBattleServerMessage 数据转换错误", err)
			return nil
		}
		err = nk.NotificationSend(ctx, battleInfo.BattleServerId, "choice", map[string]interface{}{"data": toBattleServerMessage}, int(MainServer.ServerNotificationType_ServerNotificationType_BattleChoice), battleInfo.BattleServerId, false)
		if err != nil {
			logger.Error("toBattleServerMessage 数据发送错误", err)
			return nil
		}
		mState.chioces = make(map[int64]*MainServer.BattleDecisionChoiceInfo)
	}

	// select {
	// case msg := <-bttleInfo.BattleServerMessage:
	// 	logger.Info(msg.GetContent())
	// 	// messages = append(messages, msg)
	// default:
	// }
	//假如有成员离线了，超过config.ReconnectTime 毫秒则无法再接入该战斗
	// nowTs := time.Now().UnixMilli()
	// if nowTs-trainer.SessionEndTs < config.ReconnectTime {
	// 	//return 旧的 party
	// }

	// select {
	// case msg := <-mState.aiMessages:
	// 	messages = append(messages, msg)
	// default:
	// }
	// // 检查是否有玩家消息
	// if len(messages) > 0 {
	// 	mState.lastActivity = tick // 更新最后活动时间
	// }
	// //通过partyInfo 去判断用户是否指令完成 or state 减去AI个数
	// //指令传递给battle server
	// //收到battle server result //无法在这收到 battle server没有对匹配进行设置 //partyInfo中可以加入MatchState
	// //传送给user

	// // 检查比赛是否闲置超过一定时间（例如 60 ticks）
	// idleLimit := 60 // 60 ticks 作为超时限制
	// if tick-mState.lastActivity > int64(idleLimit) && !mState.gameStarted {
	// 	logger.Info("比赛超时，终止比赛")
	// 	return nil // 返回 nil 表示比赛结束
	// }
	// for _, message := range messages {
	// 	logger.Info("收到消息 %v 来自玩家 %v", string(message.GetData()), message.GetUserId())

	// 	// 假设 OpCode == 1 表示玩家准备
	// 	if message.GetOpCode() == 1 {
	// 		playerID := message.GetUserId()
	// 		mState.playersReady[playerID] = true // 更新玩家为已准备
	// 		logger.Info("玩家 %v 已准备", playerID)

	// 		// 打印所有玩家的准备状态
	// 		for id, ready := range mState.playersReady {
	// 			logger.Info("玩家 %v 的准备状态: %v", id, ready)
	// 		}

	// 		// 检查所有玩家是否都已准备
	// 		allReady := true
	// 		for _, ready := range mState.playersReady {
	// 			if !ready {
	// 				allReady = false
	// 				break
	// 			}
	// 		}

	// 		// 如果所有玩家都准备好了，开始比赛
	// 		if allReady && len(mState.presences) == mState.playerCount {
	// 			mState.gameStarted = true
	// 			logger.Info("所有玩家都已准备好，比赛开始！")
	// 			dispatcher.BroadcastMessage(2, []byte("比赛开始！"), []runtime.Presence{}, nil, true) // 广播比赛开始消息
	// 		}
	// 	}
	// }

	return mState
}

// 比赛终止，清理比赛状态
func (m *PokeMatch) MatchTerminate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, graceSeconds int) interface{} {
	message := "比赛将在 " + strconv.Itoa(graceSeconds) + " 秒后结束。"
	reliable := true
	dispatcher.BroadcastMessage(2, []byte(message), []runtime.Presence{}, nil, reliable)

	mState, _ := state.(*PokeMatchState)
	logger.Info("比赛终止，清理状态")
	mState.presences = nil
	mState.aiPresences = nil
	mState.audiencePresences = nil
	mState.matchLabel = nil
	return mState
}

// 处理比赛信号
func (m *PokeMatch) MatchSignal(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, data string) (interface{}, string) {
	return state, "信号已接收: " + data
}

// package main

// import (
// 	"context"
// 	"github.com/heroiclabs/nakama-common/runtime"
// 	"fmt"
// )

// type MatchState struct {
// 	playersReady map[string]bool  // 保存每个玩家的准备状态
// 	connectedPlayers map[string]bool // 存储在线玩家
// 	playerCount  int              // 玩家总数
// 	gameStarted  bool             // 比赛是否开始
// }

// // 比赛初始化时调用
// func MatchInit(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, params map[string]interface{}) (interface{}, int, string) {
// 	// 初始化比赛状态，假设比赛有 4 名玩家
// 	state := &MatchState{
// 		playersReady:     make(map[string]bool),
// 		connectedPlayers: make(map[string]bool),
// 		playerCount:      4,  // 可以动态传入玩家数量
// 		gameStarted:      false,
// 	}
// 	// 比赛 tick 率，每秒运行 10 次
// 	tickRate := 10
// 	return state, tickRate, ""
// }

// // 比赛主循环，用于接收和处理消息
// func MatchLoop(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, messages []runtime.MatchData) (interface{}, error) {
// 	matchState := state.(*MatchState)

// 	// 处理收到的消息
// 	for _, message := range messages {
// 		switch message.OpCode {
// 		case 1: // OpCode 1: 玩家发送准备消息
// 			playerID := message.GetUserId()
// 			matchState.playersReady[playerID] = true
// 			logger.Info("玩家 %s 已准备", playerID)

// 			// 打印所有玩家状态
// 			fmt.Println("当前玩家状态:")
// 			for id, ready := range matchState.playersReady {
// 				status := "未准备"
// 				if ready {
// 					status = "已准备"
// 				}
// 				fmt.Printf("玩家 %s: %s\n", id, status)
// 			}

// 			// 检查是否所有玩家都已准备好
// 			allReady := true
// 			for _, ready := range matchState.playersReady {
// 				if !ready {
// 					allReady = false
// 					break
// 				}
// 			}

// 			// 如果所有玩家都准备好了，比赛开始
// 			if allReady && len(matchState.playersReady) == matchState.playerCount {
// 				matchState.gameStarted = true
// 				logger.Info("所有玩家都已准备好，比赛开始！")
// 				// 广播比赛开始的消息
// 				dispatcher.BroadcastMessage(2, []byte("比赛开始！"), nil, nil, true)
// 			}

// 		default:
// 			logger.Warn("收到未知 OpCode: %d", message.OpCode)
// 		}
// 	}

// 	return matchState, nil
// }

// // 玩家加入比赛时调用
// func MatchJoin(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presences []runtime.Presence) (interface{}, error) {
// 	matchState := state.(*MatchState)

// 	// 将新加入的玩家添加到在线玩家列表中
// 	for _, presence := range presences {
// 		matchState.connectedPlayers[presence.GetUserId()] = true
// 		logger.Info("玩家 %s 已加入比赛", presence.GetUserId())
// 	}

// 	return matchState, nil
// }

// // 玩家离开比赛时调用
// func MatchLeave(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presences []runtime.Presence) (interface{}, error) {
// 	matchState := state.(*MatchState)

// 	// 移除离开比赛的玩家
// 	for _, presence := range presences {
// 		delete(matchState.connectedPlayers, presence.GetUserId())
// 		logger.Info("玩家 %s 已离开比赛", presence.GetUserId())
// 	}

// 	// 检查是否所有玩家都已离线
// 	if len(matchState.connectedPlayers) == 0 {
// 		logger.Info("所有玩家已离线，比赛将终止")
// 		return nil, nil // 终止比赛
// 	}

// 	return matchState, nil
// }

// // 比赛终止时调用
// func MatchTerminate(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, state interface{}, graceSeconds int) (interface{}, error) {
// 	matchState := state.(*MatchState)

// 	// 清理比赛状态
// 	matchState.playersReady = nil
// 	matchState.connectedPlayers = nil
// 	matchState.playerCount = 0

// 	logger.Info("比赛已终止，状态已清理")
// 	return matchState, nil
// }
