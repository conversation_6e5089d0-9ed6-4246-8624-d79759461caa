package battle

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/inventory"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	poketeam "go-nakama-poke/team"
	"go-nakama-poke/tool"
	trainerpkg "go-nakama-poke/trainer"
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// BattleType 表示战斗类型的自定义类型
// type BattleType int

// 定义具体的战斗类型代码
// const (
//
//	BattleTypeMsg    int = 100 //消息回复
//	BattleTypeWild   int = 101 // 野生对战
//	BattleTypeNPC    int = 102 // NPC 对战
//	BattleTypePlayer int = 103 // 玩家对战
//
// )
// type BattleType int

// const (
// 	AI_4                         BattleType = 99
// 	AI_2                         BattleType = 98
// 	Msg                          BattleType = 100
// 	SingleWild                   BattleType = 101
// 	SingleWildAndDoublePokemon   BattleType = 102
// 	DoubleWild                   BattleType = 103
// 	SingleNPC                    BattleType = 104
// 	SingleNPCAndDoublePokemon    BattleType = 105
// 	DoubleNPC                    BattleType = 106
// 	SinglePlayer                 BattleType = 107
// 	SinglePlayerAndDoublePokemon BattleType = 108
// 	DoublePlayer                 BattleType = 109
// )

var (
	callTracker sync.Map     // 用于存储 tid 和最后调用时间戳
	lockMap     = sync.Map{} // 用于管理每个 tid 的锁
)

type BattleLoadMessage struct {
	toBattleServerMessage *MainServer.BattleDecisionMessage
	toTrainerMessage      *MainServer.BattleOutputMessageInfo
}

const actionLimit int64 = 10000

// getLock 获取或创建一个用于特定 tid 的锁
func getLock(tid int64) *sync.Mutex {
	lock, _ := lockMap.LoadOrStore(tid, &sync.Mutex{})
	return lock.(*sync.Mutex)
}

// LimitCallRate 限制 tid 在 10 秒内只能调用一次
func LimitCallRate(tid int64) bool {
	lock := getLock(tid)
	lock.Lock()
	defer lock.Unlock()

	now := time.Now().UnixMilli()
	value, exists := callTracker.Load(tid)

	if exists {
		lastCallTs := value.(int64)
		if now-lastCallTs < actionLimit {
			// 在 10 秒内拒绝调用
			return false
		}
	}

	// 更新最后调用时间戳
	callTracker.Store(tid, now)
	return true
}
func CleanupOldEntries() { //有人退出或者啥的，就清空一次
	now := time.Now().UnixMilli()

	// 遍历 callTracker 中的所有条目
	callTracker.Range(func(key, value interface{}) bool {
		lastCallTs := value.(int64)

		// 如果超过 10000 毫秒，删除该条目
		if now-lastCallTs > actionLimit {
			callTracker.Delete(key)
		}

		return true // 继续遍历
	})
}
func RpcBattleUrgeMesssage(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	// CleanupOldEntries()
	if trainer == nil {
		//TODO 要清空战斗信息
		logger.Error("未找到用户的 active tid %s", userID)
		return "", runtime.NewError("Not found tid", 404)
	}
	partyInfo, exists := tool.GetGlobalBattleMap().Get(userID)
	if !exists {
		return "", runtime.NewError("Not found battle info", 404)
	}
	nowTs := time.Now().UnixMilli()
	if partyInfo.UpdateTs-nowTs > 60*1000 { //1分钟
		//使用默认操作 //更新partyInfo不允许输入了
		partyInfo.LockBattleChoice = true
		content := map[string]interface{}{
			// "pokes": pokeJson,
			"bid":  partyInfo.BattleID,
			"lock": true,
		}
		//让未下指令的选手，进行随机技能
		nk.NotificationSend(ctx, userID, "b_u_m", content, int(MainServer.ServerNotificationType_ServerNotificationType_BattleUrge), partyInfo.BattleID, false)
	}
	return "{}", nil
}

func RpcBattleChoiceMesssage(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	//此处应该使用uid获取mid，没有才使用uid //TODO mid是否可以直接通过nakama获取
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	// CleanupOldEntries()
	if trainer == nil {
		//TODO 要清空战斗信息
		logger.Error("未找到用户的 active tid %s", userID)
		return "", runtime.NewError("Not found tid", 404)
	}
	partyInfo, exists := tool.GetGlobalBattleMap().Get(userID)
	battleId := ""
	if exists {
		battleId = partyInfo.BattleID
	} else {
		battleId = userID
	}
	if partyInfo.LockBattleChoice {
		return "", runtime.NewError("chioce lock", 1000)
	}
	serverID, err := AssignServerToMatch(ctx, logger, nk, battleId)
	if err != nil {
		return "", err
	}
	subject := "b_c_m"
	content, err := tool.GetJsonFromPayload(payload)
	content["bid"] = battleId
	content["tid"] = trainer.Id
	choiceRaw, choiceExists := content["choice"]
	if !choiceExists {
		logger.Warn("choice 参数不存在")
		return "", fmt.Errorf("choice not found")
	}
	choiceStr, ok := choiceRaw.(string)
	if !ok || len(choiceStr) == 0 {
		logger.Warn("choice 参数无效或为空")
		return "", fmt.Errorf("choice error")
	}
	// 检查是否存在 titem 参数
	if _, exists := content["titem"]; exists {
		choices := strings.Split(choiceStr, ",")
		if len(choices) > 0 {
			// 开始事务处理
			tx, err := db.BeginTx(ctx, nil)
			if err != nil {
				logger.Error("事务启动失败: %v", err)
				return "", fmt.Errorf("failed to begin transaction: %w", err)
			}
			defer tx.Rollback()

			for _, titem := range choices {
				if strings.HasPrefix(titem, "titem ") {
					titem = strings.TrimPrefix(titem, "titem ")
					// 用空格分割字符串
					parts := strings.Fields(titem)
					// 返回分割后的第一个部分
					if len(parts) > 0 {
						titem = parts[0]
					}
					itemInfo := &MainServer.UseItemInfo{
						ItemName: titem,
						Quantity: 1,
					}
					err := inventory.RemoveItemByItemName(ctx, tx, trainer.Id, titem, 1)
					// _, err := trainerpkg.TryUsePokeItem(ctx, logger, tx, nk, itemInfo)
					if err != nil {
						logger.Error("使用物品失败: %v, Item: %v", err, itemInfo)
						return "", fmt.Errorf("failed to use item: %w", err)
					}
				}
			}

			// 提交事务
			if err := tx.Commit(); err != nil {
				logger.Error("事务提交失败: %v", err)
				return "", fmt.Errorf("failed to commit transaction: %w", err)
			}
			content["done"] = true
		}
	}
	if err != nil {
		return "", err
	}
	persistent := true
	nk.NotificationSend(ctx, serverID, subject, content, int(MainServer.ServerNotificationType_ServerNotificationType_BattleChoice), userID, persistent)
	return "{}", nil
}

// // 同意战斗
// func RpcBattleAgree(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

// }
// //挑战对手
// func RpcBattleChallenge(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

// }

//是否需要退出战斗的逻辑
// func RpcBattleCancelPrepare(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

// }
func loadBattle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, battleInfo *tool.BattleInfo) (*BattleLoadMessage, error) {
	switch battleInfo.BattlePrepare.BattleType {
	case MainServer.BattleType_BattleType_Unknow, MainServer.BattleType_AI_2, MainServer.BattleType_AI_4, MainServer.BattleType_Msg:
		return nil, runtime.NewError("Error battle type", 400)
	case MainServer.BattleType_DoubleNPC, MainServer.BattleType_SingleNPC, MainServer.BattleType_SingleNPCAndDoublePokemon:
		return loadNpcBattle(ctx, logger, db, nk, battleInfo)
	case MainServer.BattleType_SinglePlayer, MainServer.BattleType_SinglePlayerAndDoublePokemon, MainServer.BattleType_DoublePlayer:
		return loadPlayerBattle(ctx, logger, db, nk, battleInfo)
	case MainServer.BattleType_SingleWild, MainServer.BattleType_DoubleWild, MainServer.BattleType_SingleWildAndDoublePokemon:
		return loadWildBattle(ctx, logger, db, nk, battleInfo)
	}
	return nil, runtime.NewError("Error battle type", 400)
}
func loadWildBattleByPokes(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, battleInfo *tool.BattleInfo) (*BattleLoadMessage, error) {
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, runtime.NewError("开始事务失败:"+err.Error(), 500)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	// battleStartWild(ctx, logger, db, nk, battleInfo, rid, area, count)
	wildPokeCount := 0
	AiTrainerCount := 0
	switch battleInfo.BattlePrepare.BattleType {
	case MainServer.BattleType_BattleType_Unknow, MainServer.BattleType_AI_2, MainServer.BattleType_AI_4, MainServer.BattleType_Msg:
		return nil, runtime.NewError("Error battle type", 400)
	case MainServer.BattleType_SingleWild:
		wildPokeCount = 1
		AiTrainerCount = 1
	case MainServer.BattleType_DoubleWild:
		wildPokeCount = 2
		AiTrainerCount = 2
	case MainServer.BattleType_SingleWildAndDoublePokemon:
		wildPokeCount = 2
		AiTrainerCount = 1
	}
	appearPokes, err := poke.AppearPokes(ctx, logger, tx, nk, battleInfo.BattlePrepare.BattleMatchAiMaker.ReginId, battleInfo.BattlePrepare.BattleMatchAiMaker.AreaId, battleInfo.BattlePrepare.BattleMatchAiMaker.EncounterMethod, wildPokeCount)
	if err != nil || len(appearPokes) == 0 {
		return nil, err
	}
	for _, poke := range appearPokes {
		pokes, exists := battleInfo.BattlePokes[poke.Tid]
		if !exists {
			pokes = map[int64]*MainServer.Poke{}
		}
		pokes[poke.Id] = poke
		battleInfo.BattlePokes[poke.Tid] = pokes
	}
	initTeamInfoMap := make(map[string][]*MainServer.BattleInitTeamInfo)
	partyIndex := 1
	for _, partyInfo := range battleInfo.PartyInfos {
		partyUserInfos, partyPokes, err := buildPalyerInitTeamInfos(*partyInfo, partyIndex)
		if err != nil {
			return nil, err
		}
		initTeamInfoMap[partyInfo.PartyId] = partyUserInfos
		for _, poke := range partyPokes {
			pokes, exists := battleInfo.BattlePokes[poke.Tid]
			if !exists {
				pokes = map[int64]*MainServer.Poke{}
			}
			pokes[poke.Id] = poke
			battleInfo.BattlePokes[poke.Tid] = pokes
		}
		partyIndex++
	}

	aiPartyTids := func() []int64 {
		// 创建一个新的切片存储修改后的 Tids
		var updatedTids []int64
		for i := 0; i < AiTrainerCount; i++ {
			updatedTids = append(updatedTids, int64(trainerpkg.WildTrainerId)-int64(i))
		}
		return updatedTids
	}()
	initTeamInfos, err := buildAiInitTeamInfos(aiPartyTids, appearPokes, partyIndex)
	if err != nil {
		return nil, err
	}
	initTeamInfoMap["ai_1"] = initTeamInfos
	return NewBattleLoadMessage(battleInfo.BattleID, battleInfo.BattlePrepare.BattleType, initTeamInfoMap, battleInfo.PartyInfos)
}
func loadNpcBattle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, battleInfo *tool.BattleInfo) (*BattleLoadMessage, error) {
	// battleStartWild(ctx, logger, db, nk, battleInfo, rid, area, count)
	configs := make(map[string]*MainServer.BattleNpc)
	var battlePokeTeamInfos []*BattlePokeTeamInfo
	var npcPokes []*MainServer.Poke
	for _, npcid := range battleInfo.BattlePrepare.BattleMatchAiMaker.NpcIds {
		npc := &MainServer.BattleNpc{}
		npcConfig, exists := poketeam.GetNpcConfig(npcid)
		if !exists {
			return nil, runtime.NewError("Npc config not found", 400)
		}
		npc.Config = npcConfig
		//随机获取config.PokeTeamIds中的一个
		pokeTeamId := npcConfig.PokeTeamIds[rand.Intn(len(npcConfig.PokeTeamIds))]
		pokeConfig, exists := poketeam.GetPokeTeam(pokeTeamId)
		if !exists {
			return nil, runtime.NewError("Poke Team config not found", 400)
		}
		npc.PokeTeam = pokeConfig
		configs[npcid] = npc
		battlePokeTeamInfos = append(battlePokeTeamInfos, &BattlePokeTeamInfo{
			npcNameId:  npcid,
			pokes:      pokeConfig.Pokes,
			partyIndex: 1,
		})
		for _, pokeInfo := range pokeConfig.Pokes {
			if pokeInfo.Poke == nil {
				continue
			}
			if pokeInfo.Poke.Level == 0 {
				pokeInfo.Poke.Level = 1
			}
			npcPokes = append(npcPokes, pokeInfo.Poke)
		}
	}
	initTeamInfoMap := make(map[string][]*MainServer.BattleInitTeamInfo)
	partyIndex := 1
	for _, partyInfo := range battleInfo.PartyInfos {
		partyUserInfos, partyPokes, err := buildPalyerInitTeamInfos(*partyInfo, partyIndex)
		if err != nil {
			return nil, err
		}
		initTeamInfoMap[partyInfo.PartyId] = partyUserInfos
		for _, poke := range partyPokes {
			pokes, exists := battleInfo.BattlePokes[poke.Tid]
			if !exists {
				pokes = map[int64]*MainServer.Poke{}
			}
			pokes[poke.Id] = poke
			battleInfo.BattlePokes[poke.Tid] = pokes
		}
		partyIndex++
	}

	aiPartyTids := func() []int64 {
		// 创建一个新的切片存储修改后的 Tids
		var updatedTids []int64
		for index := range battleInfo.BattlePrepare.BattleMatchAiMaker.NpcIds {
			// WildTrainerId - index 来计算新的值
			updatedTids = append(updatedTids, int64(trainerpkg.NPCTrainerId)-int64(index))
		}
		return updatedTids
	}()
	initTeamInfos, err := buildAiInitTeamInfos(aiPartyTids, npcPokes, partyIndex)
	if err != nil {
		return nil, err
	}
	initTeamInfoMap["ai_1"] = initTeamInfos
	return NewBattleLoadMessage(battleInfo.BattleID, battleInfo.BattlePrepare.BattleType, initTeamInfoMap, battleInfo.PartyInfos)
	// return nil, runtime.NewError("NpcBattlePrepare err", 400)
}
func loadWildBattle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, battleInfo *tool.BattleInfo) (*BattleLoadMessage, error) {
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, runtime.NewError("开始事务失败:"+err.Error(), 500)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	// battleStartWild(ctx, logger, db, nk, battleInfo, rid, area, count)
	wildPokeCount := 0
	AiTrainerCount := 0
	switch battleInfo.BattlePrepare.BattleType {
	case MainServer.BattleType_BattleType_Unknow, MainServer.BattleType_AI_2, MainServer.BattleType_AI_4, MainServer.BattleType_Msg:
		return nil, runtime.NewError("Error battle type", 400)
	case MainServer.BattleType_SingleWild:
		wildPokeCount = 1
		AiTrainerCount = 1
	case MainServer.BattleType_DoubleWild:
		wildPokeCount = 2
		AiTrainerCount = 2
	case MainServer.BattleType_SingleWildAndDoublePokemon:
		wildPokeCount = 2
		AiTrainerCount = 1
	}
	appearPokes := []*MainServer.Poke{}
	if battleInfo.BattlePrepare.BattleMatchAiMaker != nil && battleInfo.BattlePrepare.BattleMatchAiMaker.AiType == MainServer.BattleMatchAiType_BattleMatchAiType_SummonWild {
		appearPokes = battleInfo.BattleSummonPokes
	} else {
		appearPokes, err := poke.AppearPokes(ctx, logger, tx, nk, battleInfo.BattlePrepare.BattleMatchAiMaker.ReginId, battleInfo.BattlePrepare.BattleMatchAiMaker.AreaId, battleInfo.BattlePrepare.BattleMatchAiMaker.EncounterMethod, wildPokeCount)
		if err != nil || len(appearPokes) == 0 {
			return nil, err
		}
	}
	for _, poke := range appearPokes {
		pokes, exists := battleInfo.BattlePokes[poke.Tid]
		if !exists {
			pokes = map[int64]*MainServer.Poke{}
		}
		pokes[poke.Id] = poke
		battleInfo.BattlePokes[poke.Tid] = pokes
	}
	// messages := make([]proto.Message, len(pokes))
	// for i, item := range pokes {
	// 	messages[i] = item // poke 已经实现了 proto.Message 接口
	// }
	// pokeJson, err := tool.ProtosToJson(messages) //是不是需要重新转换
	// if err != nil {
	// 	return err
	// }
	// 获取调用者的客户端 ID
	// senderID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// serverID, err := AssignServerToMatch(ctx, logger, nk, partyInfo.BattleID)
	// if err != nil {
	// 	return err
	// }
	// partyUserInfos :=
	initTeamInfoMap := make(map[string][]*MainServer.BattleInitTeamInfo)
	partyIndex := 1
	for _, partyInfo := range battleInfo.PartyInfos {
		partyUserInfos, partyPokes, err := buildPalyerInitTeamInfos(*partyInfo, partyIndex)
		if err != nil {
			return nil, err
		}
		initTeamInfoMap[partyInfo.PartyId] = partyUserInfos
		for _, poke := range partyPokes {
			pokes, exists := battleInfo.BattlePokes[poke.Tid]
			if !exists {
				pokes = map[int64]*MainServer.Poke{}
			}
			pokes[poke.Id] = poke
			battleInfo.BattlePokes[poke.Tid] = pokes
		}
		partyIndex++
	}

	aiPartyTids := func() []int64 {
		// 创建一个新的切片存储修改后的 Tids
		var updatedTids []int64
		for i := 0; i < AiTrainerCount; i++ {
			updatedTids = append(updatedTids, int64(trainerpkg.WildTrainerId)-int64(i))
		}
		return updatedTids
	}()
	initTeamInfos, err := buildAiInitTeamInfos(aiPartyTids, appearPokes, partyIndex)
	if err != nil {
		return nil, err
	}
	initTeamInfoMap["ai_1"] = initTeamInfos
	// for i := 0; i < wildPokeCount; i++ {
	// 	partyUserInfos, partyPokes, err := buildAiPartyUserInfos()
	// }
	// battleInfo.PartyInfos

	// partaTids := partyInfo.Tids
	// partyAPokes := &[]*MainServer.Poke{}
	// partyAUserInfos, err := buildPartyUserInfos(partaTids, nil, partyAPokes)
	// if err != nil {
	// 	return err
	// }
	// partbTids := func() []int64 {
	// 	// 创建一个新的切片存储修改后的 Tids
	// 	var updatedTids []int64
	// 	for index := range partyInfo.Tids {
	// 		// WildTrainerId - index 来计算新的值
	// 		updatedTids = append(updatedTids, int64(trainerpkg.WildTrainerId)-int64(index))
	// 	}
	// 	return updatedTids
	// }()
	// partyBPokes := &[]*MainServer.Poke{}
	// partyBUserInfos, err := buildPartyUserInfos(partbTids, pokes, partyBPokes)
	// if err != nil {
	// 	return err
	// }
	// battleLoadMessage, err := NewBattleLoadMessage(battleInfo.BattleID, partyUserInfoMap, battleInfo.PartyInfos)
	return NewBattleLoadMessage(battleInfo.BattleID, battleInfo.BattlePrepare.BattleType, initTeamInfoMap, battleInfo.PartyInfos)
	// content := map[string]interface{}{
	// 	// "pokes": pokeJson,
	// 	"bid":   battleInfo.BattleID,
	// 	"party": partyUserInfoMap,
	// 	// "party_a": map[string]interface{}{
	// 	// 	"uinfos": partyAUserInfos,
	// 	// 	"pid":    partyInfo.PartyId,
	// 	// 	"lid":    partyInfo.LeaderId,
	// 	// },
	// 	// "party_b": map[string]interface{}{
	// 	// 	// "ai": trainerpkg.WildTrainerId,
	// 	// 	"uinfos": partyBUserInfos,
	// 	// },
	// }

	// battleLoadMessage := BattleLoadMessage{}
	// battleLoadMessage.toBattleServerMessage = content
	// trainers := []*MainServer.Trainer{}
	// for _, partyInfo := range battleInfo.PartyInfos {
	// 	for _, trainer := range partyInfo.Trainers {
	// 		trainers = append(trainers, trainer)
	// 	}
	// }
	// ts, err := tool.ProtoToBase64(&MainServer.TrainersResult{
	// 	Trainers: trainers,
	// })
	// if err != nil {
	// 	return nil, err
	// }
	// result := map[string]interface{}{
	// 	"type": "prepare",
	// 	"data": ts,
	// 	"bid":  battleId,
	// }
	// // partyInfo.PartAPokes = *partyAPokes
	// // partyInfo.PartBPokes = *partyBPokes
	// // receiverID := sessionID
	// // senderID := "dcb891ea-a311-4681-9213-6741351c9994"
	// code := battleInfo.BattlePrepare.BattleType //开启一个野生对战
	// persistent := false
	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// allTids := append(partaTids, partbTids...)
	// err = sendPrepareMessage(ctx, logger, nk, partyInfo.BattleID, allTids)
	// if err != nil {
	// 	return err
	// }
	// // subject := "b_s_w"
	// nk.NotificationSend(ctx, serverID, "b_s_w", content, code, userID, persistent)
	// return nil

	// return matchId, nil
}
func loadPlayerBattle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, battleInfo *tool.BattleInfo) (*BattleLoadMessage, error) {
	initTeamInfoMap := make(map[string][]*MainServer.BattleInitTeamInfo)
	partyIndex := 1
	for _, partyInfo := range battleInfo.PartyInfos {
		partyUserInfos, partyPokes, err := buildPalyerInitTeamInfos(*partyInfo, partyIndex)
		if err != nil {
			return nil, err
		}
		initTeamInfoMap[partyInfo.PartyId] = partyUserInfos
		for _, poke := range partyPokes {
			pokes, exists := battleInfo.BattlePokes[poke.Tid]
			if !exists {
				pokes = map[int64]*MainServer.Poke{}
			}
			pokes[poke.Id] = poke
			battleInfo.BattlePokes[poke.Tid] = pokes
		}
		partyIndex++
	}
	return NewBattleLoadMessage(battleInfo.BattleID, battleInfo.BattlePrepare.BattleType, initTeamInfoMap, battleInfo.PartyInfos)
}
func NewBattleLoadMessage(battleId string, battleType MainServer.BattleType, initTeamInfoMap map[string][]*MainServer.BattleInitTeamInfo, partyInfos map[string]*MainServer.PartyInfo) (*BattleLoadMessage, error) {
	// content := map[string]interface{}{
	// 	"bid":   battleId,
	// 	"party": partyUserInfoMap,
	// }
	trainerInfos := []*MainServer.BattlePrepareOutputTrainerInfo{}
	teamInfos := []*MainServer.BattleInitTeamInfo{}
	for _, teamInfo := range initTeamInfoMap {
		teamInfos = append(teamInfos, teamInfo...)
		for _, info := range teamInfo {
			trainerInfos = append(trainerInfos, info.TrainerInfo)
		}

		// for _, trainer := range partyInfo.Trainers {
		// 	trainerInfos = append(trainerInfos, &MainServer.BattlePrepareOutputTrainerInfo{
		// 		Trainer:     trainer,
		// 		PsPlayerKey: "p1",
		// 		IsAi:        false,
		// 	})
		// 	// trainers = append(trainers, trainer)
		// }
	}
	// ts, err := tool.ProtoToBase64(&MainServer.TrainersResult{
	// 	Trainers: trainers,
	// })
	// if err != nil {
	// 	return nil, err
	// }
	toBattleServerMessage := &MainServer.BattleDecisionMessage{
		BattleId:     battleId,
		BattleType:   battleType,
		DecisionType: MainServer.BattleDecisionType_Init,
		TeamInfos:    teamInfos,
	}
	message := &MainServer.BattleOutputMessage{
		MessageType:  MainServer.BattleOutputMessageType_Prepare,
		TrainerInfos: trainerInfos,
	}
	toTrainerMessage := &MainServer.BattleOutputMessageInfo{
		BattleId:   battleId,
		BattleType: battleType,
		Messages:   []*MainServer.BattleOutputMessage{message},
	}
	// result := map[string]interface{}{
	// 	"type": "prepare",
	// 	"data": ts,
	// 	"bid":  battleId,
	// }
	battleLoadMessage := &BattleLoadMessage{
		toBattleServerMessage: toBattleServerMessage,
		toTrainerMessage:      toTrainerMessage,
	}
	return battleLoadMessage, nil
}

func RpcMatchList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("trainer not found", 400)
	}
	if !LimitCallRate(trainer.Id) {
		return "", runtime.NewError("battle count erro", 400)
	}
	var prepareInfo = &MainServer.BattlePrepare{}
	if err := tool.Base64ToProto(payload, prepareInfo); err != nil {
		logger.Error("解析BattlePrepare 失败: %v", err) // 记录解析失败
		return "", runtime.NewError("payload err", 400)
	}
	if prepareInfo.BattleMatchMaker == nil {
		return "", runtime.NewError("battle list info erro", 400)
	}
	// partyCount := 1
	// partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
	// if exists {
	// 	partyCount = len(partyInfo.Trainers)
	// }

	matchLabel := matchLabelBy(trainer, prepareInfo.BattleType, prepareInfo.BattleMatchMaker)
	limit := 10
	authoritative := true
	label := ""
	// minSize := int(matchLabel.Min) - partyCount
	// maxSize := int(matchLabel.Max) - partyCount
	minSize := 0
	maxSize := 4
	query := MatchMakerToQuery(prepareInfo.BattleMatchMaker)
	matches, err := nk.MatchList(ctx, limit, authoritative, label, &minSize, &maxSize, query)
	if err != nil {
		logger.WithField("err", err).Error("Match listings error.")
		return "", nil
	}
	if len(matches) < 1 {
		// RpcBattlePrepare(ctx, logger, db, nk, payload)
		matchId, err := startBattleMatch(ctx, logger, nk, trainer, prepareInfo, nil)
		if err != nil {
			return "", err
		}
		matchListResult := &MainServer.MatchResult{}
		match := &MainServer.BattleMatchInfo{
			MatchId:      matchId,
			Label:        matchLabel,
			TrainerCount: 0,
		}
		matchListResult.NewMatchId = matchId
		matchListResult.Matchs = append(matchListResult.Matchs, match)
		return tool.ProtoToBase64(matchListResult)
	} else {
		matchListResult := &MainServer.MatchResult{}
		for _, v := range matches {
			matchLabel := &MainServer.BattleMatchLabel{}
			err := json.Unmarshal([]byte(v.Label.Value), matchLabel)
			if err != nil {
				logger.Error("解析失败:", err)
			}
			match := &MainServer.BattleMatchInfo{
				MatchId:      v.MatchId,
				Label:        matchLabel,
				TrainerCount: v.Size,
			}
			matchListResult.Matchs = append(matchListResult.Matchs, match)
		}
		return tool.ProtoToBase64(matchListResult)
	}
	return "", nil
}

// 所有的战斗开始都要通过这个，如果有party信息，可以存储一份，用作验证
// 这里可以先让客户端将party成员的信息发过来，//后续如果要优化的话，在进行两方确认
// 返回match id
func RpcBattlePrepare(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer, err := validateTrainer(ctx)
	if err != nil {
		return "", err
	}
	var prepareInfo = &MainServer.BattlePrepare{}
	if err := tool.Base64ToProto(payload, prepareInfo); err != nil {
		logger.Error("解析BattlePrepare 失败: %v", err) // 记录解析失败
		return "", runtime.NewError("payload err", 400)
	}
	if prepareInfo.BattleMatchAiMaker == nil && prepareInfo.BattleMatchMaker == nil {
		return "", runtime.NewError("battle info erro", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("开始事务失败:"+err.Error(), 500)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	// matchInfo := &MainServer.BattleMatchInfo{}
	// // playerCount := 2
	// switch prepareInfo.BattleType {
	// case MainServer.BattleType_SingleWild, MainServer.BattleType_SingleNPC, MainServer.BattleType_SingleNPCAndDoublePokemon, MainServer.BattleType_SingleWildAndDoublePokemon:
	// 	matchInfo.Min = 1
	// 	matchInfo.Max = 1
	// case MainServer.BattleType_SinglePlayer:
	// 	matchInfo.Min = 2
	// 	matchInfo.Max = 2
	// case MainServer.BattleType_SinglePlayerAndDoublePokemon:
	// 	matchInfo.Min = 2
	// 	matchInfo.Max = 3
	// case MainServer.BattleType_DoubleNPC, MainServer.BattleType_DoubleWild, MainServer.BattleType_DoublePlayer:
	// 	matchInfo.Min = 3
	// 	matchInfo.Max = 4
	// }
	// tool.Base64ToProto(payload)s
	// 从 payload 解析 JSON 数据
	// data, err := tool.GetJsonFromPayload(payload)
	// if err != nil {
	// 	logger.Error("Failed to parse payload: %v", err)
	// 	return "", runtime.NewError("Invalid payload", 400)
	// }

	// ridRaw, ridOk := data["rid"]
	// if !ok {
	// 	logger.Warn("Missing 'rid' field in payload")
	// 	return "", runtime.NewError("Missing 'rid' field", 400)
	// }
	// areaRaw, areaOk := data["area"]
	// if !ok {
	// 	logger.Warn("Missing 'area' field in payload")
	// 	return "", runtime.NewError("Missing 'area' field", 400)
	// }
	switch prepareInfo.BattleType {
	case MainServer.BattleType_BattleType_Unknow, MainServer.BattleType_AI_2, MainServer.BattleType_AI_4, MainServer.BattleType_Msg:
		return "", runtime.NewError("Error battle type", 400)
	case MainServer.BattleType_DoubleNPC, MainServer.BattleType_SingleNPC, MainServer.BattleType_SingleNPCAndDoublePokemon, MainServer.BattleType_SinglePlayer, MainServer.BattleType_SinglePlayerAndDoublePokemon, MainServer.BattleType_DoublePlayer, MainServer.BattleType_SingleWild, MainServer.BattleType_DoubleWild, MainServer.BattleType_SingleWildAndDoublePokemon:
		pokes := make([]*MainServer.Poke, 0)
		if prepareInfo.BattleMatchAiMaker != nil && prepareInfo.BattleMatchAiMaker.AiType == MainServer.BattleMatchAiType_BattleMatchAiType_SummonWild {
			for _, pokeId := range prepareInfo.BattleMatchAiMaker.SummonPokeIds {
				pokeInfo := poke.QueryPokeById(ctx, tx, 0, pokeId)
				if pokeInfo != nil && pokeInfo.Born.Tid == trainer.Id && (pokeInfo.Born == nil || (pokeInfo.Born != nil && pokeInfo.Born.IsBattle == false)) {
					pokes = append(pokes, pokeInfo)
				}
			}
			if len(pokes) == 0 {
				err = runtime.NewError("未找到合适的精灵", 400)
				return "", err
			}
		}
		return startBattleMatch(ctx, logger, nk, trainer, prepareInfo, pokes)

		// matchaLabel := matchLabelBy(trainer, prepareInfo.BattleType, prepareInfo.BattleMatchMaker)
		// matchId, err := createCustomMatch(ctx, logger, nk, prepareInfo.BattleType, matchaLabel)
		// if err != nil {
		// 	return "", err
		// }
		// battleInfo := &tool.BattleInfo{
		// 	BattleID:             matchId,
		// 	MatchLabel:           matchaLabel,
		// 	CreateTrainer:        trainer,
		// 	BattlePrepare:        prepareInfo,
		// 	BattlePokes:          map[int64]map[int64]*MainServer.Poke{},
		// 	BattleServerMessages: make(chan *MainServer.BattleServerMessage, 10),
		// }
		// partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
		// //默认个人是没有群的
		// if !exists {
		// 	partyInfo := &tool.PartyInfo{
		// 		Trainers: map[int64]*MainServer.Trainer{trainer.Id: trainer},
		// 	}
		// 	battleInfo.PartyInfos = map[string]*tool.PartyInfo{trainer.Uid: partyInfo}
		// } else {
		// 	battleInfo.PartyInfos = map[string]*tool.PartyInfo{partyInfo.PartyId: partyInfo}
		// }
		// tool.GetGlobalBattleMap().Set(matchId, battleInfo)
		// return matchId, nil
	}
	// matchId, err := CreateCustomMatch(ctx, logger, nk, trainer, prepareInfo.BattleType)
	// if err != nil {
	// 	return "", err
	// }
	// return matchId, nil

	// switch prepareInfo.BattleType {
	// case MainServer.BattleType_BattleType_Unknow:
	// case MainServer.BattleType_AI_2:
	// case MainServer.BattleType_AI_4:
	// case MainServer.BattleType_Msg:
	// 	return "", runtime.NewError("Error battle type", 400)
	// case MainServer.BattleType_DoubleNPC:
	// case MainServer.BattleType_SingleNPC:
	// case MainServer.BattleType_SingleNPCAndDoublePokemon:
	// 	return npcBattlePrepare(ctx, logger, db, nk, trainer, prepareInfo)
	// case MainServer.BattleType_SinglePlayer:
	// case MainServer.BattleType_SinglePlayerAndDoublePokemon:
	// case MainServer.BattleType_DoublePlayer:
	// 	return playerBattlePrepare(ctx, logger, db, nk, trainer, prepareInfo)
	// case MainServer.BattleType_SingleWild:
	// case MainServer.BattleType_DoubleWild:
	// case MainServer.BattleType_SingleWildAndDoublePokemon:
	// 	return wildBattlePrepare(ctx, logger, db, nk, trainer, prepareInfo)
	// }
	return "", runtime.NewError("Not found battle type", 400)
	// matchIdRaw, ok := data["matchId"]
	// if !ok {
	// 	logger.Warn("Missing 'matchId' field in payload")
	// 	return "", runtime.NewError("Missing 'matchId' field", 400)
	// }
	// 验证参数是否存在
	// partyRaw, ok := data["party"]
	// var party []string
	// if !ok {
	// 	party = []string{}
	// } else {
	// 	// 转换参数类型
	// 	party, ok = partyRaw.([]string)
	// 	if !ok {
	// 		logger.Warn("Invalid type for 'party': expected array of strings")
	// 		return "", runtime.NewError("Invalid type for 'party'", 400)
	// 	}
	// }

	// rid, ok := ridRaw.(string)
	// if !ok {
	// 	logger.Warn("Invalid type for 'rid': expected string")
	// 	return "", runtime.NewError("Invalid type for 'rid'", 400)
	// }
	// area, ok := areaRaw.(string)
	// if !ok {
	// 	logger.Warn("Invalid type for 'area': expected string")
	// 	return "", runtime.NewError("Invalid type for 'area'", 400)
	// }
	// matchId, ok := matchIdRaw.(string)
	// if !ok {
	// 	logger.Warn("Invalid type for 'matchId': expected string")
	// 	return "", runtime.NewError("Invalid type for 'matchId'", 400)
	// }
	// 默认参数设置
	// if countRaw, exists := data["count"]; exists {
	// 	if parsedCount, ok := countRaw.(float64); ok { // JSON 数字默认解析为 float64
	// 		count = int(parsedCount)
	// 	} else {
	// 		logger.Warn("Invalid type for 'count': expected number")
	// 		return "", runtime.NewError("Invalid type for 'count'", 400)
	// 	}
	// }
	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// trainer := tool.GetActiveTrainerByUid(userID)
	// if !LimitCallRate(trainer.Id) {
	// 	return "", runtime.NewError("battle count erro", 400)
	// }
	// matchId, err = CreateCustomMatch(ctx, logger, nk, trainer, SingleWild)
	// if err != nil {
	// 	return "", err
	// }
	// battleInfo, exists := tool.GetGlobalBattleMap().Get(matchId)
	// if battleInfo.CreateTrainer.Id != trainer.Id {
	// 	return "", runtime.NewError("start battle match erro", 400)
	// }
	// //不存在则表明是个人
	// if !exists {
	// 	battleInfo = &tool.PartyInfo{
	// 		Tids:     []int64{trainer.Id},
	// 		BattleID: userID,
	// 		PartyId:  userID,
	// 		LeaderId: userID,
	// 		CreateTs: time.Now().UnixMilli(),
	// 	}
	// 	tool.GetGlobalBattleMap().Set(userID, battleInfo)
	// 	logger.Info("create PartyInfo %d", battleInfo.CreateTs)
	// }
	// logger.Info("exists PartyInfo %d", time.Now().UnixMilli()-battleInfo.CreateTs)
	// battleInfo.CreateTs = time.Now().UnixMilli()
	// ClearServerToMatch(ctx, logger, nk, battleInfo.BattleID)
	// count := len(battleInfo.Tids)
	// // tool.GetGlobalBattleMap().Set(userID, partyInfo)
	// // 调用 BattleStartWild 函数
	// err = battleStartWild(ctx, logger, db, nk, battleInfo, rid, area, count)
	// if err != nil {
	// 	logger.Error("BattleStartWild failed: %v", err)
	// 	return "", runtime.NewError("Failed to start battle", 500)
	// }

	// logger.Info("Battle prepared successfully with rid: %s, party size: %d", rid, len(party))
	// return battleInfo.BattleID, nil
}
func startBattleMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, trainer *MainServer.Trainer, prepareInfo *MainServer.BattlePrepare, summonPokes []*MainServer.Poke) (string, error) {
	matchaLabel := matchLabelBy(trainer, prepareInfo.BattleType, prepareInfo.BattleMatchMaker)
	matchId, err := createCustomMatch(ctx, logger, nk, prepareInfo.BattleType, matchaLabel)
	if err != nil {
		return "", err
	}
	battleInfo := &tool.BattleInfo{
		BattleID:             matchId,
		MatchLabel:           matchaLabel,
		CreateTrainer:        trainer,
		BattlePrepare:        prepareInfo,
		BattleSummonPokes:    summonPokes,
		BattlePokes:          map[int64]map[int64]*MainServer.Poke{},
		BattleServerMessages: make(chan *MainServer.BattleServerMessage, 20),
	}
	partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
	//默认个人是没有群的
	if !exists {
		partyInfo := &MainServer.PartyInfo{
			Trainers: map[int64]*MainServer.Trainer{trainer.Id: trainer},
			PartyId:  trainer.Uid,
			Leader:   trainer,
		}
		battleInfo.PartyInfos = map[string]*MainServer.PartyInfo{trainer.Uid: partyInfo}
	} else {
		battleInfo.PartyInfos = map[string]*MainServer.PartyInfo{partyInfo.PartyId: partyInfo}
	}
	tool.GetGlobalBattleMap().Set(matchId, battleInfo)
	return matchId, nil
}

//	type PartyUserInfo struct {
//		ID   int64  `json:"id"`
//		Team string `json:"team"`
//	}
type BattlePokeTeamInfo struct {
	tid        int64
	npcNameId  string
	pokes      []*MainServer.BattlePokeInfo
	partyIndex int
}

func buildAiInitTeamInfos(tids []int64, pokes []*MainServer.Poke, partyIndex int) ([]*MainServer.BattleInitTeamInfo, error) {
	initTeamInfos := make([]*MainServer.BattleInitTeamInfo, len(tids))
	// *partyPokes = append(*partyPokes, pokes...)
	// 计算每个 tid 能分配到的 Pokemon 数量
	totalTids := len(tids)
	totalPokes := len(pokes)
	if totalTids == 0 {
		return nil, runtime.NewError("no tids available", 500)
	}
	if totalPokes < totalTids { //精灵至少要等于训练家
		return nil, runtime.NewError("poke len < tids len", 500)
	}

	// 均分逻辑
	startIndex := 0
	for index, tid := range tids {
		// 每个 tid 应分配的最少数量
		remainingTids := totalTids - index
		remainingPokes := totalPokes - startIndex
		pokesForThisTid := (remainingPokes + remainingTids - 1) / remainingTids // 向上取整

		// 分配 Pokemon
		endIndex := startIndex + pokesForThisTid
		if endIndex >= totalPokes {
			endIndex = totalPokes
		}

		initTeamInfos[index] = &MainServer.BattleInitTeamInfo{
			Team: poke.ConvertPokesToPackedFormat(pokes[startIndex:endIndex]),
			TrainerInfo: &MainServer.BattlePrepareOutputTrainerInfo{
				PsPlayerKey: fmt.Sprintf("p%d", (index+1)*partyIndex),
				IsAi:        true,
				Trainer: &MainServer.Trainer{
					Id: tid,
					SessionInfo: &MainServer.TrainerSessionInfo{
						BattlePokes: pokes[startIndex:endIndex],
					},
				},
			},
		}

		// 更新起始索引
		startIndex = endIndex
	}
	return initTeamInfos, nil
}

// partyIndex 从 1开始
func buildPalyerInitTeamInfos(partyInfo MainServer.PartyInfo, partyIndex int) ([]*MainServer.BattleInitTeamInfo, []*MainServer.Poke, error) {
	partyPokes := []*MainServer.Poke{}
	initTeamInfos := []*MainServer.BattleInitTeamInfo{}
	trainerIndex := 1
	for _, trainer := range partyInfo.Trainers {
		pokes, _ := tool.GetTrainerAroundPokes(trainer.Id)
		// if exists {
		// 	pokes = tpokes
		// }
		partyPokes = append(partyPokes, pokes...)
		initTeamInfos = append(initTeamInfos, &MainServer.BattleInitTeamInfo{
			Team: poke.ConvertPokesToPackedFormat(pokes),
			TrainerInfo: &MainServer.BattlePrepareOutputTrainerInfo{
				Trainer:     tool.CopySafeTrainer(trainer),
				PsPlayerKey: fmt.Sprintf("p%d", trainerIndex*partyIndex),
				IsAi:        false,
			},
		})
		trainerIndex++
		// partyUserInfos[index] = PartyUserInfo{
		// 	ID:   trainer.Id,
		// 	Team: poke.ConvertPokesToPackedFormat(pokes),
		// }
	}
	// if pokes == nil {
	// 	for index, tid := range tids {
	// 		tpokes, exists := tool.GetTrainerAroundPokes(tid)
	// 		if exists {
	// 			pokes = tpokes
	// 		}
	// 		*partyPokes = append(*partyPokes, pokes...)
	// 		partyUserInfos[index] = PartyUserInfo{
	// 			ID:   tid,
	// 			Team: poke.ConvertPokesToPackedFormat(pokes),
	// 		}
	// 	}
	// } else {
	// 	*partyPokes = append(*partyPokes, pokes...)
	// 	// 计算每个 tid 能分配到的 Pokemon 数量
	// 	totalTids := len(tids)
	// 	totalPokes := len(pokes)
	// 	if totalTids == 0 {
	// 		return nil, runtime.NewError("no tids available", 500)
	// 	}
	// 	if totalPokes < totalTids { //精灵至少要等于训练家
	// 		return nil, runtime.NewError("poke len < tids len", 500)
	// 	}

	// 	// 均分逻辑
	// 	startIndex := 0
	// 	for index, tid := range tids {
	// 		// 每个 tid 应分配的最少数量
	// 		remainingTids := totalTids - index
	// 		remainingPokes := totalPokes - startIndex
	// 		pokesForThisTid := (remainingPokes + remainingTids - 1) / remainingTids // 向上取整

	// 		// 分配 Pokemon
	// 		endIndex := startIndex + pokesForThisTid
	// 		if endIndex > totalPokes {
	// 			endIndex = totalPokes
	// 		}

	// 		partyUserInfos[index] = PartyUserInfo{
	// 			ID:   tid,
	// 			Team: poke.ConvertPokesToPackedFormat(pokes[startIndex:endIndex]),
	// 		}

	// 		// 更新起始索引
	// 		startIndex = endIndex
	// 	}
	// }
	// partyUserInfos := make([]PartyUserInfo, len(tids))
	// for index, tid := range tids {
	// 	if pokes == nil {
	// 		tpokes, exists := tool.GetTrainerAroundPokes(tid)
	// 		if exists {
	// 			pokes = tpokes
	// 		}
	// 		partyUserInfos[index] = PartyUserInfo{
	// 			ID:   tid,
	// 			Team: poke.ConvertPokesToPackedFormat(pokes),
	// 		}
	// 	} else {
	// 		if len(pokes) < len(tids) {
	// 			return nil, runtime.NewError("poke len < tids len", 500)
	// 		}
	// 		partyUserInfos[index] = PartyUserInfo{
	// 			ID:   tid,
	// 			Team: poke.ConvertPokesToPackedFormat([]*MainServer.Poke{pokes[index]}),
	// 		}

	// 	}
	// }
	return initTeamInfos, partyPokes, nil
}

// func battleStartWild(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, partyInfo *tool.PartyInfo, rid string, area string, count int) error {
// 	pokes, err := poke.AppearPokes(ctx, logger, db, nk, rid, area, count)
// 	if err != nil || len(pokes) == 0 {
// 		return err
// 	}
// 	// messages := make([]proto.Message, len(pokes))
// 	// for i, item := range pokes {
// 	// 	messages[i] = item // poke 已经实现了 proto.Message 接口
// 	// }
// 	// pokeJson, err := tool.ProtosToJson(messages) //是不是需要重新转换
// 	// if err != nil {
// 	// 	return err
// 	// }
// 	// 获取调用者的客户端 ID
// 	// senderID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	serverID, err := AssignServerToMatch(ctx, logger, nk, partyInfo.BattleID)
// 	if err != nil {
// 		return err
// 	}
// 	// partyUserInfos :=
// 	partaTids := partyInfo.Tids
// 	partyAPokes := &[]*MainServer.Poke{}
// 	partyAUserInfos, err := buildPartyUserInfos(partaTids, nil, partyAPokes)
// 	if err != nil {
// 		return err
// 	}
// 	partbTids := func() []int64 {
// 		// 创建一个新的切片存储修改后的 Tids
// 		var updatedTids []int64
// 		for index := range partyInfo.Tids {
// 			// WildTrainerId - index 来计算新的值
// 			updatedTids = append(updatedTids, int64(trainerpkg.WildTrainerId)-int64(index))
// 		}
// 		return updatedTids
// 	}()
// 	partyBPokes := &[]*MainServer.Poke{}
// 	partyBUserInfos, err := buildPartyUserInfos(partbTids, pokes, partyBPokes)
// 	if err != nil {
// 		return err
// 	}
// 	content := map[string]interface{}{
// 		// "pokes": pokeJson,
// 		"bid": partyInfo.BattleID,
// 		"party_a": map[string]interface{}{
// 			"uinfos": partyAUserInfos,
// 			"pid":    partyInfo.PartyId,
// 			"lid":    partyInfo.LeaderId,
// 		},
// 		"party_b": map[string]interface{}{
// 			// "ai": trainerpkg.WildTrainerId,
// 			"uinfos": partyBUserInfos,
// 		},
// 	}
// 	partyInfo.PartAPokes = *partyAPokes
// 	partyInfo.PartBPokes = *partyBPokes
// 	// receiverID := sessionID
// 	// senderID := "dcb891ea-a311-4681-9213-6741351c9994"
// 	code := int(SingleWild) //开启一个野生对战
// 	persistent := true
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	allTids := append(partaTids, partbTids...)
// 	err = sendPrepareMessage(ctx, logger, nk, partyInfo.BattleID, allTids)
// 	if err != nil {
// 		return err
// 	}
// 	// subject := "b_s_w"
// 	nk.NotificationSend(ctx, serverID, "b_s_w", content, code, userID, persistent)
// 	return nil
// }
// func sendPrepareMessage(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, battleId string, allTids []int64) error {
// 	trainers := tool.GetActiveTrainersByIds(allTids)
// 	alltrainers := append([]*MainServer.Trainer{}, trainers...)
// 	for i := 0; i < len(allTids); i++ {
// 		if allTids[i] < 0 {
// 			alltrainers = append(alltrainers, trainerpkg.GetAiTrainer(ctx, logger, trainerpkg.WildTrainerId, allTids[i], ""))
// 		}
// 	}
// 	for i := 0; i < len(trainers); i++ {
// 		ts, err := tool.ProtoToBase64(&MainServer.TrainersResult{
// 			Trainers: alltrainers,
// 		})
// 		if err != nil {
// 			return err
// 		}
// 		result := map[string]interface{}{
// 			"type": "prepare",
// 			"data": ts,
// 			"bid":  battleId,
// 		}
// 		nk.NotificationSend(ctx, trainers[i].Uid, "b_p", result, int(Msg), battleId, false)
// 	}
// 	return nil
// }

// 从匹配开始战斗
func BattleStartMatch(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, match string) (string, error) {
	// content := map[string]interface{}{
	// 	"pokes": pokeJson,
	// 	"party_a": map[string]interface{}{
	// 		"bid":  partyInfo.BattleID,
	// 		"uids": partyInfo.Uids,
	// 		"pid":  partyInfo.PartyId,
	// 		"lid":  partyInfo.LeaderId,
	// 	},
	// 	"party_b": nil,
	// }
	// 确保 payload 包含正确的 key
	// rid, exists := tool.GetStringFromPayload("r", payload) // 假设有一个函数解析 rid
	// if !exists {
	// 	return "", runtime.NewError("无效的区域ID", 400) // 错误处理，区域ID不存在
	// }

	// // 解析 count，默认值为1
	// count, exists := tool.GetIntFromPayload("c", payload)
	// if !exists {
	// 	count = 1
	// }
	// pokes, err := poke.AppearPoke(ctx, logger, db, nk, rid, count)
	// if err != nil || len(pokes) == 0 {
	// 	return "", err
	// }
	// messages := make([]proto.Message, len(pokes))
	// for i, item := range pokes {
	// 	messages[i] = item // poke 已经实现了 proto.Message 接口
	// }
	// pokeJson, err := tool.ProtosToJson(messages) //是不是需要重新转换
	// if err != nil {
	// 	return "", err
	// }
	// // 获取调用者的客户端 ID
	// senderID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// serverID, err := AssignServerToMatch(ctx, logger, nk, senderID)
	// if err != nil {
	// 	return "", err
	// }
	// subject := "b_s"
	// content := map[string]interface{}{
	// 	"pokes": pokeJson,
	// }
	// // receiverID := sessionID
	// // senderID := "dcb891ea-a311-4681-9213-6741351c9994"
	// code := BattleTypeWild //开启一个野生对战
	// persistent := true

	// nk.NotificationSend(ctx, serverID, subject, content, code, senderID, persistent)

	return "{}", nil
}
func RpcBattleServerNormalEvent(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	message := &MainServer.BattleServerMessage{}
	err := tool.Base64ToProto(payload, message)
	if err != nil {
		return "", err
	}
	return "", nil
}
func BattleNpcResp(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	battleServerMessage := &MainServer.BattleServerMessage{}
	err := tool.Base64ToProto(payload, battleServerMessage)
	if err != nil {
		return "", fmt.Errorf("failed to parse payload: %w", err)
	}
	bttleInfo, exists := tool.GetGlobalBattleMap().Get(battleServerMessage.BattleId)
	if !exists {
		//删除战斗数据
		return "", runtime.NewError("invalid battle ID", 400)
	}

	// 使用非阻塞的select语句发送消息
	select {
	case bttleInfo.BattleServerMessages <- battleServerMessage:
		// 消息发送成功
		logger.Info("消息已成功发送到通道")
	default:
		// 通道已满或没有接收者，记录日志但不阻塞
		logger.Warn("无法立即发送消息到通道，可能通道已满或没有接收者")
	}

	return "消息已处理", nil
}

// func BattleNpcResp(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	battleServerMessage := &MainServer.BattleServerMessage{}
// 	err := tool.Base64ToProto(payload, battleServerMessage)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to parse payload: %w", err)
// 	}
// 	bttleInfo, exists := tool.GetGlobalBattleMap().Get(battleServerMessage.BattleId)
// 	if !exists {
// 		//删除战斗数据
// 		return "", runtime.NewError("invalid battle ID", 400)
// 	}
// 	bttleInfo.BattleServerMessages <- battleServerMessage
// 	// 解析 payload
// 	// data, err := tool.GetJsonFromPayload(payload)
// 	// if err != nil {
// 	// 	return "", fmt.Errorf("failed to parse payload: %w", err)
// 	// }

// 	// 获取 battleId
// 	// battleId, ok := data["bid"].(string)
// 	// if !ok {
// 	// 	return "", runtime.NewError("invalid battle ID", 400)
// 	// }

// 	// // 获取消息类型
// 	// messageType, ok := data["type"].(string)
// 	// if !ok {
// 	// 	return "", runtime.NewError("invalid message type", 400)
// 	// }

// 	// // 处理消息类型为 "st" 或 "init"
// 	// if messageType == "st" {
// 	// 	logger.Info("Received message: battleId=%s, type=st", battleId)
// 	// 	return "是st消息", nil
// 	// } else if messageType == "init" {
// 	// 	logger.Info("Received initialization message: battleId=%s, type=init", battleId)
// 	// 	// return handleInitMessage(ctx, logger, nk, battleId, data)
// 	// }

// 	// // 获取 tid
// 	// tidf, ok := data["tid"].(float64)
// 	// if !ok {
// 	// 	return "", runtime.NewError("invalid trainer ID", 400)
// 	// }
// 	// tid := int64(tidf)

// 	// 获取 trainer
// 	// trainer := tool.GetActiveTrainerByTid(battleServerMessage.Tid)
// 	// if trainer == nil {
// 	// 	return "", runtime.NewError("trainer not found", 404)
// 	// }

// 	// 处理 "uppoke" 消息
// 	// if uppoke, ok := data["uppoke"].(string); ok {
// 	// 	_, err := handleUppokeMessage(ctx, logger, db, battleId, uppoke, data)
// 	// 	if err != nil {
// 	// 		logger.Error("Failed to handleUppokeMessage to trainer %s: %v", trainer.Uid, err)
// 	// 		return "", err
// 	// 	}
// 	// }
// 	// if titem, ok := data["titem"].(string); ok {
// 	// 	parts := strings.Split(titem, "|")
// 	// 	if len(parts) == 2 {
// 	// 		pid, err := strconv.ParseInt(parts[1], 10, 64)
// 	// 		if err == nil {
// 	// 			localItem, ex := item.GetItemByName(parts[0])
// 	// 			if ex && item.IsPokeBall(*localItem) { //2表示ball
// 	// 				err := handleBallTitemMessage(ctx, logger, db, battleId, trainer, pid)
// 	// 				if err != nil {
// 	// 					logger.Error("Failed to handleBallTitemMessage to trainer %s: %v", trainer.Uid, err)
// 	// 				}
// 	// 			}
// 	// 		}
// 	// 	}
// 	// }

// 	// 处理 "win" 消息
// 	// if win, ok := data["win"].(bool); ok && win {
// 	// 	wpart, ok := data["wpart"].(string)
// 	// 	if !ok {
// 	// 		return "", runtime.NewError("invalid wpart", 400)
// 	// 	}
// 	// 	err := handleWinMessage(ctx, logger, db, battleId, wpart)
// 	// 	if err != nil {
// 	// 		return "", err
// 	// 	}
// 	// }
// 	return "", nil
// 	// 处理默认消息
// 	// return handleDefaultMessage(ctx, logger, nk, battleId, trainer, data)
// }

// func handleInitMessage(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, battleId string, data map[string]interface{}) (string, error) {
// 	partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 	if !exists {
// 		return "", runtime.NewError("battle ID not found in global battle map", 404)
// 	}

// 	trainers := tool.GetActiveTrainersByIds(partyInfo.Tids)
// 	for _, trainer := range trainers {
// 		err := nk.NotificationSend(ctx, trainer.Uid, "b_i", data, int(MainServer.BattleType_Msg), trainer.Uid, true)
// 		if err != nil {
// 			logger.Error("Failed to send notification to trainer %s: %v", trainer.Uid, err)
// 		}
// 	}

// 	logger.Info("Initialization message processed: battleId=%s", battleId)
// 	return "是init消息", nil
// }
// func handleBallTitemMessage(ctx context.Context, logger runtime.Logger, db *sql.DB, battleId string, trainer *MainServer.Trainer, pid int64) error {
// 	partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 	if !exists {
// 		return runtime.NewError("battle ID not found in global battle map", 404)
// 	}
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return fmt.Errorf("failed to begin transaction: %w", err)
// 	}
// 	defer tx.Rollback()
// 	for _, partPoke := range partyInfo.PartBPokes {
// 		if partPoke.Id == pid {
// 			err = trainerpkg.TakeWildPoke(ctx, logger, tx, trainer, partPoke)
// 			if err != nil {
// 				return err
// 			}
// 			logger.Info("handleBallTitemMessage: battleId=%s", battleId)
// 			break
// 		}
// 	}
// 	if err := tx.Commit(); err != nil {
// 		return fmt.Errorf("failed to commit transaction: %w", err)
// 	}
// 	return nil
// }
// func handleUppokeMessage(ctx context.Context, logger runtime.Logger, db *sql.DB, battleId, uppoke string, data map[string]interface{}) (string, error) {
// 	partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 	if !exists {
// 		return "", runtime.NewError("battle ID not found in global battle map", 404)
// 	}

// 	type Data struct {
// 		HpSub int32 `json:"hpSub"`
// 		Tid   int64 `json:"tid"`
// 		ID    int64 `json:"id"`
// 	}

// 	// 解析 JSON 字符串
// 	var datas []Data
// 	if err := json.Unmarshal([]byte(uppoke), &datas); err != nil {
// 		return "", fmt.Errorf("failed to unmarshal uppoke JSON: %w", err)
// 	}

// 	// 创建一个以 ID 为键的 map，用于快速查找更新
// 	dataMap := make(map[int64]int32, len(datas))
// 	for _, data := range datas {
// 		dataMap[data.ID] = data.HpSub
// 	}

// 	// 更新 Pokes
// 	upPokes := updatePokeHp(append(partyInfo.PartAPokes, partyInfo.PartBPokes...), dataMap, logger)
// 	if len(upPokes) > 0 {
// 		if err := updateDatabase(ctx, db, upPokes); err != nil {
// 			return "", err
// 		}
// 	}
// 	return "是uppoke消息", nil
// }

func updatePokeHp(pokes []*MainServer.Poke, dataMap map[int64]int32, logger runtime.Logger) []*MainServer.Poke {
	var updatedPokes []*MainServer.Poke
	for _, poke := range pokes {
		if newHpSub, ok := dataMap[poke.Id]; ok && poke.HpSub != newHpSub {
			poke.HpSub = newHpSub
			updatedPokes = append(updatedPokes, poke)
		}
	}
	return updatedPokes
}

func updateDatabase(ctx context.Context, db *sql.DB, pokes []*MainServer.Poke) error {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	for _, upoke := range pokes {
		if err := poke.UpdatePokeData(ctx, tx, upoke); err != nil {
			return fmt.Errorf("failed to update Poke data: %w", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}

// func handleWinMessage(ctx context.Context, logger runtime.Logger, db *sql.DB, battleId, wpart string) error {
// 	partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 	if !exists {
// 		return runtime.NewError("battle ID not found in global battle map", 404)
// 	}

// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return fmt.Errorf("failed to begin transaction: %w", err)
// 	}
// 	defer tx.Rollback()

// 	if wpart == "A" {
// 		err = BattleEnd(ctx, logger, tx, &partyInfo.PartAPokes, &partyInfo.PartBPokes)
// 	} else if wpart == "B" {
// 		err = BattleEnd(ctx, logger, tx, &partyInfo.PartBPokes, &partyInfo.PartAPokes)
// 	}

// 	if err != nil {
// 		return err
// 	}

// 	if err := tx.Commit(); err != nil {
// 		return fmt.Errorf("failed to commit transaction: %w", err)
// 	}

// 	// logger.Info("Processed win message: battleId=%s, wpart=%s", battleId, wpart)
// 	return nil
// }

func handleDefaultMessage(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, battleId string, trainer *MainServer.Trainer, data map[string]interface{}) (string, error) {
	receiverData, ok := data["data"].(string)
	if !ok {
		return "", runtime.NewError("invalid receiver data", 400)
	}

	_, ok = data["code"].(float64)
	if !ok {
		return "", runtime.NewError("invalid receiver code", 400)
	}

	subject := "b_n_r"
	content := map[string]interface{}{
		"data": receiverData,
		"bid":  data["bid"],
		"type": data["type"],
	}
	partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
	if !exists {
		return "", runtime.NewError("battle ID not found in global battle map", 404)
	}
	partyInfo.LockBattleChoice = false
	if err := nk.NotificationSend(ctx, trainer.Uid, subject, content, int(MainServer.ServerNotificationType_ServerNotificationType_BattleResult), trainer.Uid, false); err != nil {
		return "", fmt.Errorf("failed to send notification: %w", err)
	}

	// logger.Infof("Default message processed: trainerId=%d", trainer.Id)
	return "{}", nil
}

// func BattleNpcResp(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 确保 payload 包含正确的 key
// 	data, err := tool.GetJsonFromPayload(payload)
// 	if err != nil {
// 		return "", err
// 	}
// 	battleId, ok := data["bid"].(string) //
// 	if !ok {
// 		return "", runtime.NewError("无效的战斗ID", 400)
// 	}
// 	messageType, ok := data["type"].(string) //: p , st, err
// 	if !ok {
// 		return "", runtime.NewError("无效的用户messageType", 400)
// 	}
// 	// 假设键的值是字符串类型
// 	tidf, ok := data["tid"].(float64) //
// 	if !ok {
// 		if messageType == "st" { //发送给观看玩家
// 			logger.Info("接收到了:" + battleId + " 战斗类型" + messageType)
// 			return "是st消息", nil
// 		} else if messageType == "init" {
// 			logger.Info("接收到了:" + battleId + " 初始化信息" + messageType)
// 			partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 			if exists {
// 				trainers := tool.GetActiveTrainersByIds(partyInfo.Tids)
// 				for i := 0; i < len(trainers); i++ {
// 					nk.NotificationSend(ctx, trainers[i].Uid, "b_i", data, int(Msg), trainers[i].Uid, true)
// 				}
// 				return "是init消息", nil
// 			}
// 		}
// 		return "", runtime.NewError("无效的用户ID", 400)
// 	}
// 	tid := int64(tidf)
// 	trainer := tool.GetActiveTrainerByTid(tid)
// 	if trainer == nil {
// 		//TODO 是否需要重连
// 		return "", runtime.NewError("无效的用户ID", 400)
// 	}
// 	uppoke, ok := data["uppoke"].(string)
// 	if ok {
// 		partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 		if exists {
// 			type Data struct {
// 				HpSub int32 `json:"HpSub"`
// 				Tid   int64 `json:"tid"`
// 				ID    int64 `json:"id"`
// 			}
// 			var datas []Data
// 			// 解析 JSON 字符串
// 			err := json.Unmarshal([]byte(uppoke), &datas)
// 			if err != nil {
// 				return "", fmt.Errorf("failed to unmarshal uppoke JSON: %w", err)
// 			}
// 			// 创建一个以 ID 为键的 map，用于快速查找更新
// 			dataMap := make(map[int64]int32, len(datas))
// 			for _, data := range datas {
// 				dataMap[data.ID] = data.HpSub
// 			}
// 			upPokes := []*MainServer.Poke{}
// 			partPokes := append(partyInfo.PartAPokes, partyInfo.PartBPokes...)
// 			for _, apoke := range partPokes {
// 				if newHpSub, ok := dataMap[apoke.Id]; ok && apoke.HpSub != newHpSub {
// 					apoke.HpSub = newHpSub
// 					upPokes = append(upPokes, apoke)
// 				}
// 			}
// 			if len(upPokes) > 0 {

// 				tx, err := db.BeginTx(ctx, nil)
// 				if err != nil {
// 					return "", fmt.Errorf("failed to begin transaction: %w", err)
// 				}
// 				defer tx.Rollback()
// 				for _, upPoke := range upPokes {
// 					poke.UpdatePokeData(ctx, tx, upPoke)
// 				}
// 				if err := tx.Commit(); err != nil {
// 					logger.Error("事务提交失败: %v", err)
// 					return "", fmt.Errorf("failed to commit transaction: %w", err)
// 				}
// 			}

// 			logger.Info("接收到了uppoke:" + uppoke)
// 			return "是uppoke消息", nil
// 		}
// 	}
// 	win, ok := data["win"].(bool)
// 	if ok && win {
// 		wpart, ok := data["wpart"].(string)
// 		if !ok {
// 			return "", runtime.NewError("无效的wpart", 400)
// 		}
// 		partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 		if exists {
// 			tx, err := db.BeginTx(ctx, nil)
// 			if err != nil {
// 				return "", fmt.Errorf("failed to begin transaction: %w", err)
// 			}
// 			defer tx.Rollback()
// 			if wpart == "A" {
// 				err := BattleEnd(ctx, logger, tx, &partyInfo.PartAPokes, &partyInfo.PartBPokes)
// 				if err != nil {
// 					return "", err
// 				}
// 				// for _, poke := range partyInfo.PartAPokes {
// 				// 	poke.Experience += 100
// 				// }
// 			} else if wpart == "B" {
// 				err := BattleEnd(ctx, logger, tx, &partyInfo.PartBPokes, &partyInfo.PartAPokes)
// 				if err != nil {
// 					return "", err
// 				}
// 				// for _, poke := range partyInfo.PartBPokes {
// 				// 	poke.Experience += 100
// 				// }
// 			}
// 			// 提交事务
// 			if err := tx.Commit(); err != nil {
// 				logger.Error("事务提交失败: %v", err)
// 				return "", fmt.Errorf("failed to commit transaction: %w", err)
// 			}
// 		}
// 		// winData, ok := data["wdata"].(string)
// 		// logger.Info("接收到了winData:" + winData)
// 		// winData = strings.TrimSpace(winData)
// 		// parts := strings.Split(winData, "&")
// 		// tidStr := strconv.FormatInt(tid, 10)
// 		// for _, part := range parts {
// 		// 	if part == tidStr {
// 		// 		partyInfo, exists := tool.GetGlobalBattleMap().Get(battleId)
// 		// 		if exists {
// 		// 			partyInfo.PartAPokes
// 		// 		}
// 		// 	}
// 		// }
// 	}

// 	receiverData, ok := data["data"].(string)
// 	if !ok {
// 		return "", runtime.NewError("无效的用户data", 400)
// 	}

// 	receiverCode, ok := data["code"].(float64)
// 	if !ok {
// 		return "", runtime.NewError("无效的用户code", 400)
// 	}
// 	// receiverId, exists := tool.GetStringFromPayload("id", payload) // 假设有一个函数解析 rid
// 	// if !exists {
// 	// 	return "", runtime.NewError("无效的用户ID", 400) // 错误处理，区域ID不存在
// 	// }
// 	// data, exists := tool.GetStringFromPayload("data", payload)
// 	// if !exists {
// 	// 	return "", runtime.NewError("无效的用户data", 400)
// 	// }
// 	sessionID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	// 从缓存中获取客户端信息
// 	value, ok := clientCache.Load(sessionID)
// 	if !ok {
// 		return "", runtime.NewError("Client not registered as battle server", 5)
// 	}

// 	clientInfo, ok := value.(ClientInfo)
// 	if !ok {
// 		return "", runtime.NewError("Failed to retrieve client info", 13)
// 	}

// 	// 检查是否是战斗服务器
// 	if !clientInfo.IsBattleServer {
// 		logger.Warn("Client %s is not a battle server, ignoring resource update", sessionID)
// 		return "{\"message\":\"Ignored: client is not a battle server\"}", nil
// 	}

// 	subject := "b_n_r"
// 	content := map[string]interface{}{
// 		"data": receiverData,
// 		"bid":  battleId,
// 		"type": messageType,
// 	}
// 	// code := 101
// 	persistent := false
// 	nk.NotificationSend(ctx, trainer.Uid, subject, content, int(receiverCode), "", persistent)

// 	return "{}", nil
// }

//	func BattleEnd(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
//		return "{}", nil
//	}
func RpcBattleEscape(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return "{}", nil
}
