package poke

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"os"
	"strconv"
	"strings"

	"go-nakama-poke/proto/MainServer"
)

// b := 64.0  // 基础经验值
// L := 50.0  // 对手宝可梦等级
// Lp := 45.0 // 胜利方宝可梦等级
// a := 1.5   // 对手属于训练师
// e := 1.5   // 持有幸运蛋
// f := 1.2   // 高亲密度
// p := 1.0   // 无加成道具
// s := 1.0   // 参与战斗的宝可梦数量
// t := 1.5   // 外来的宝可梦
// v := 1.2   // 达到可进化等级
// CalculateExperience 计算经验值

type GrowthRate struct {
	Levels map[int32]int32 `json:"levels"`
}

var pokemonGrowthRate map[string]GrowthRate

func CalculateExperience(
	b, L, Lp, a, e, f, p, s, t, v float64,
) float64 {
	// 公式分解
	numerator := 2.0*L + 10.0
	denominator := L + Lp + 10.0
	scalingFactor := math.Pow(numerator/denominator, 2.5) + 1.0

	// 经验值计算
	exp := (b * L / 5.0) * (1.0 / s) * scalingFactor * t * e * v * f * p
	return exp
}
func LoadGrowthData() {
	// 读取 JSON 文件
	data, err := os.ReadFile("/nakama/data/growthRate.json")
	if err != nil {
		log.Fatalf("Failed to read growthRate.json file: %v", err)
	}

	// 临时结构用于解析 JSON
	type TempGrowthRate struct {
		Levels map[string]int32 `json:"levels"`
	}

	var tempData map[string]TempGrowthRate
	if err := json.Unmarshal(data, &tempData); err != nil {
		log.Fatalf("Failed to parse growthRate.json: %v", err)
	}

	// 转换键为整数
	pokemonGrowthRate = make(map[string]GrowthRate)
	for key, tempGrowthRate := range tempData {
		levels := make(map[int32]int32)
		for strKey, value := range tempGrowthRate.Levels {
			intKey, err := strconv.Atoi(strKey)
			if err != nil {
				log.Fatalf("Invalid level key: %v", err)
			}
			levels[int32(intKey)] = value
		}
		pokemonGrowthRate[key] = GrowthRate{Levels: levels}
	}

	log.Printf("Successfully loaded %d growthRate entries into pokemonGrowthRate.", len(pokemonGrowthRate))
}

// func LoadGrowthData() {
// 	// 读取 JSON 文件
// 	data, err := os.ReadFile("/nakama/data/growthRate.json")
// 	if err != nil {
// 		log.Fatalf("Failed to read growthRate.json file: %v", err)
// 	}

// 	// 解析 JSON 数据直接到全局变量
// 	if err := json.Unmarshal(data, &pokemonGrowthRate); err != nil {
// 		log.Fatalf("Failed to parse growthRate.json: %v", err)
// 	}

// 	log.Printf("Successfully loaded %d growthRate entries into pokemonGrowthRate.", len(pokemonGrowthRate))
// }

func GetPokemonGrowthRate(growth string) (*GrowthRate, bool) {
	growthRate, exists := pokemonGrowthRate[growth]
	if !exists {
		return nil, exists
	}
	return &growthRate, true
}

// EvolutionResult 包含进化结果的信息
type EvolutionResult struct {
	CanEvolve     bool             // 是否可以进化
	EvolvedPoke   *MainServer.Poke // 进化后的宝可梦，如果无法进化则为nil
	EvolutionType string           // 进化类型
	Reason        string           // 无法进化的原因或进化方式的描述
}

// 处理字符串比较：去除空格并转小写
func normalizeString(s string) string {
	return strings.ToLower(strings.TrimSpace(s))
}

// CheckEvolution 检查宝可梦是否满足进化条件
func CheckEvolution(poke *MainServer.Poke, evoName string) *EvolutionResult {
	// 获取宝可梦的数据
	pokemonData, exists := GetPokemonInfo(poke.Name)
	if !exists {
		return &EvolutionResult{
			CanEvolve: false,
			Reason:    "无法获取宝可梦数据",
		}
	}

	// 检查宝可梦是否有进化形态
	if len(pokemonData.Evos) == 0 {
		return &EvolutionResult{
			CanEvolve: false,
			Reason:    "该宝可梦没有进化形态",
		}
	}

	// 检查指定的进化形态是否在可能的进化列表中
	evolvesPossible := false
	for _, possibleEvo := range pokemonData.Evos {
		if normalizeString(possibleEvo) == normalizeString(evoName) {
			evolvesPossible = true
			break
		}
	}

	if !evolvesPossible {
		return &EvolutionResult{
			CanEvolve: false,
			Reason:    "该宝可梦无法进化成" + evoName,
		}
	}

	// 获取进化后形态的数据
	evoPokemonData, exists := GetPokemonInfo(evoName)
	if !exists {
		return &EvolutionResult{
			CanEvolve: false,
			Reason:    "无法获取进化形态数据",
		}
	}

	// 检查进化类型
	evoType := evoPokemonData.EvoType

	// 处理不同的进化类型
	switch evoType {
	case "":
		// 如果没有指定进化类型，但有进化等级，则视为等级进化
		if evoPokemonData.EvoLevel > 0 {
			if poke.Level >= evoPokemonData.EvoLevel {
				return &EvolutionResult{
					CanEvolve:     true,
					EvolutionType: "level",
					Reason:        "达到进化等级",
				}
			} else {
				return &EvolutionResult{
					CanEvolve: false,
					Reason:    "等级不足，需要达到" + strconv.Itoa(int(evoPokemonData.EvoLevel)) + "级",
				}
			}
		}
		// 如果没有指定进化类型也没有进化等级，返回默认值
		return &EvolutionResult{
			CanEvolve: false,
			Reason:    "未指定进化类型或等级",
		}

	case "level":
		// 等级进化
		if poke.Level >= evoPokemonData.EvoLevel {
			return &EvolutionResult{
				CanEvolve:     true,
				EvolutionType: "level",
				Reason:        "达到进化等级",
			}
		} else {
			return &EvolutionResult{
				CanEvolve: false,
				Reason:    "等级不足，需要达到" + strconv.Itoa(int(evoPokemonData.EvoLevel)) + "级",
			}
		}

	case "levelFriendship":
		// 友情度+等级进化//这个只要等级到达30就可以
		if poke.Level >= 30 {
			return &EvolutionResult{
				CanEvolve:     true,
				EvolutionType: "levelFriendship",
				Reason:        "友情度足够高，可以进化/等级到达30",
			}
		} else {
			return &EvolutionResult{
				CanEvolve: false,
				Reason:    "友情度不足，需要达到220以上/等级到达30",
			}
		}

	case "levelMove":
		// 习得特定技能后等级进化
		hasMove := false
		requiredMove := evoPokemonData.EvoMove

		for _, move := range poke.Moves {
			if normalizeString(move.Name) == normalizeString(requiredMove) {
				hasMove = true
				break
			}
		}

		if hasMove {
			return &EvolutionResult{
				CanEvolve:     true,
				EvolutionType: "levelMove",
				Reason:        "学会特定技能" + requiredMove + "，可以进化",
			}
		} else {
			return &EvolutionResult{
				CanEvolve: false,
				Reason:    "需要学会技能" + requiredMove,
			}
		}

	case "levelHold":
		// 携带特定道具后等级进化
		itemName := ""
		if poke.ItemInfo != nil {
			itemName = poke.ItemInfo.ItemName
		}
		if normalizeString(itemName) == normalizeString(evoPokemonData.EvoItem) {
			return &EvolutionResult{
				CanEvolve:     true,
				EvolutionType: "levelHold",
				Reason:        "携带特定道具" + evoPokemonData.EvoItem + "，可以进化",
			}
		} else {
			return &EvolutionResult{
				CanEvolve: false,
				Reason:    "需要携带道具" + evoPokemonData.EvoItem,
			}
		}

	case "useItem":
		// 使用特定道具进化
		return &EvolutionResult{
			CanEvolve:     false,
			EvolutionType: "useItem",
			Reason:        "需要使用道具" + evoPokemonData.EvoItem + "进化",
		}

	case "trade":
		// 交换进化
		return &EvolutionResult{
			CanEvolve:     false,
			EvolutionType: "trade",
			Reason:        "需要通过交换进化",
		}

	default:
		// 其他进化条件
		return &EvolutionResult{
			CanEvolve:     false,
			EvolutionType: evoType,
			Reason:        "特殊进化条件: " + evoPokemonData.EvoCondition,
		}
	}
}

// EvolveByLevel 执行基于等级的宝可梦进化
// 只处理纯粹基于等级的进化，返回进化后的宝可梦或nil（如果不能进化）
func EvolveByLevel(ctx context.Context, tx *sql.Tx, poke *MainServer.Poke, evoName string) (*MainServer.Poke, error) {
	// 获取宝可梦数据
	pokemonData, exists := GetPokemonInfo(poke.Name)
	if !exists {
		return nil, fmt.Errorf("无法获取宝可梦数据: %s", poke.Name)
	}

	// 获取进化后的形态名称
	if len(pokemonData.Evos) == 0 {
		return nil, fmt.Errorf("该宝可梦没有进化形态: %s", poke.Name)
	}

	// evoName := pokemonData.Evos[0]

	// 先检查宝可梦是否能够进化
	result := CheckEvolution(poke, evoName)

	// 如果不能进化，或者不是基于等级的进化，就返回nil
	if !result.CanEvolve || (result.EvolutionType != "level" && result.EvolutionType != "") {
		return nil, nil
	}

	// 使用前面获取的evoName
	poke.Name = evoName
	// 保留原始宝可梦的属性，但更新其名称和相关数据
	// evolvedPoke := &MainServer.Poke{
	// 	Id:         poke.Id,
	// 	TrainerId:  poke.TrainerId,
	// 	Uid:        poke.Uid,
	// 	Name:       evoName,
	// 	Nickname:   poke.Nickname,
	// 	Level:      poke.Level,
	// 	Experience: poke.Experience,
	// 	Ivs:        poke.Ivs,
	// 	Evs:        poke.Evs,
	// 	Moves:      poke.Moves,
	// 	Nature:     poke.Nature,
	// 	Ability:    poke.Ability,
	// 	Shiny:      poke.Shiny,
	// 	Gender:     poke.Gender,
	// 	Happiness:  poke.Happiness,
	// 	Friendship: poke.Friendship,
	// 	Ball:       poke.Ball,
	// 	Item:       poke.Item,
	// 	IsEgg:      poke.IsEgg,
	// 	Dead:       poke.Dead,
	// 	Hp:         poke.Hp,
	// 	Status:     poke.Status,
	// 	Tags:       poke.Tags,
	// }

	// 更新宝可梦数据库记录
	err := UpdatePokeData(ctx, tx, poke)
	if err != nil {
		return nil, fmt.Errorf("更新宝可梦数据失败: %w", err)
	}

	return poke, nil
}
