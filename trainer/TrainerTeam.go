package trainer

import (
	"context"
	"database/sql"
	"go-nakama-poke/inventory"
	"go-nakama-poke/item"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 升级团队的等级
func tryUpTeamLevel(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer) error {
	// trainer.TeamInfo.Contribution += amount
	// _, err := UpsertTrainer(ctx, logger, tx, trainer)
	// if err != nil {
	// 	trainer.TeamInfo.Contribution -= amount // 恢复原来的团队贡献值
	// 	logger.Error("addTeamContribution failed to upsert trainer: %v", err)
	// 	return err
	// }
	return nil
}

// 捐赠Coin到团队贡献值
func tryDonateTeamByCoin(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.Coin < amount {
		return runtime.NewError("金币不足", 400)
	}
	trainer.Coin -= amount
	trainer.TeamInfo.Contribution += amount //先1:1兑换
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution -= amount // 恢复原来的团队贡献值
		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

// 尝试消耗team贡献值兑换经验
func tryConsumeTeamContributionToExp(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.TeamInfo.Contribution < amount {
		return runtime.NewError("贡献值不足", 400)
	}
	trainer.TeamInfo.Contribution -= amount
	trainer.TeamInfo.Exp += int32(amount)
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution += amount // 恢复原来的团队贡献值
		trainer.TeamInfo.Exp -= int32(amount)
		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

func tryBuyStoreItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, itemNameId string, inventoryType MainServer.InventoryType, count int32, buySiteType MainServer.BuySiteType, teamType MainServer.TrainerTeam) error {
	localItem, exists := item.GetItemByName(itemNameId)
	if !exists {
		return runtime.NewError("物品不存在", 400)
	}
	itemSaleType := MainServer.InventoryItemSaleType_ItemSaleType_Normal
	if buySiteType == MainServer.BuySiteType_BuySiteTypeTeamStore {
		if localItem.TeamCost <= 0 {
			return runtime.NewError("该物品不能通过团队商店购买", 400)
		}
		itemSaleType = MainServer.InventoryItemSaleType_ItemSaleType_Team_Normal
	} else if buySiteType == MainServer.BuySiteType_BuySiteTypeNormal {
		if localItem.Cost <= 0 {
			return runtime.NewError("该物品不能通过普通商店购买", 400)
		}
		itemSaleType = MainServer.InventoryItemSaleType_ItemSaleType_Normal
	}
	cost := int64(localItem.TeamCost * count)
	if teamType != trainer.Team {
		cost = int64(float64(cost) * 1.2)
	}
	if trainer.TeamInfo.Contribution < cost {
		return runtime.NewError("贡献值不足", 400)
	}
	trainer.TeamInfo.Contribution -= cost
	err := inventory.AddItemToInventory(ctx, tx, trainer.Id, itemNameId, count, itemSaleType, teamType)
	if err != nil {
		trainer.TeamInfo.Contribution += cost // 恢复原来的团队贡献值
		logger.Error("buyStoreItem failed to addItemToInventory: %v", err)
		return err
	}
	_, err = UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution += cost // 恢复原来的团队贡献值
		logger.Error("buyStoreItem failed to upsert trainer: %v", err)
		return err
	}
	return nil
}
