package trainer

import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strconv"

	"github.com/heroiclabs/nakama-common/runtime"
)

func RpcInviteParty(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取发起邀请的训练师
	proposer := tool.GetActiveTrainerByCtx(ctx)
	if proposer == nil {
		return "", runtime.NewError("未找到发起邀请的训练师", 400)
	}

	// 检查发起者是否是队长
	proposerParty, proposerInParty := tool.GetGlobalPartyMap().Get(proposer.Id)
	if proposerInParty && proposerParty.Leader.Id != proposer.Id {
		return "", runtime.NewError("只有队长才能发起战斗邀请", 403)
	}

	// 解析请求参数
	var param MainServer.InvitePartyParam
	if err := tool.Base64ToProto(payload, &param); err != nil {
		logger.Error("解析InviteBattleParam失败: %v", err)
		return "", runtime.NewError("请求参数无效", 400)
	}

	// 获取目标训练师
	target := tool.GetActiveTrainerByTid(param.TargetTrainerId)
	if target == nil {
		return "", runtime.NewError("目标训练师不存在或不在线", 404)
	}
	// 发送通知给目标训练师
	if err := tool.SendInvitePartyNotification(ctx, logger, nk, proposer, target); err != nil {
		logger.Error("发送通知失败: %v", err)
		return "", runtime.NewError("发送邀请通知失败", 500)
	}
	return "", nil
}
func RpcGetPartyInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	tid, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}
	info, exists := tool.GetGlobalPartyMap().Get(tid)
	if exists {
		return tool.ProtoToBase64(info)
	}
	return "", nil
}
