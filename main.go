package main

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/admin"
	"go-nakama-poke/battle"
	"go-nakama-poke/email"
	"go-nakama-poke/hook"
	"go-nakama-poke/inventory"
	"go-nakama-poke/item"
	"go-nakama-poke/market"
	"go-nakama-poke/poke"
	"go-nakama-poke/quest"
	poketeam "go-nakama-poke/team"
	"go-nakama-poke/tool"
	"go-nakama-poke/trainer"
	"go-nakama-poke/user"

	// "encoding/json"
	// "errors"
	// "io/ioutil"
	// "net/http"

	"github.com/heroiclabs/nakama-common/runtime"
)

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	// market.InitMarket(ctx, logger, db)
	hook.HookEvent(initializer)
	tool.InitBuntdb()
	tool.InitGlobalBattleMap()
	trainer.Initiation(ctx, logger, db)
	poke.InitPokes(ctx, logger, db)
	// trainer.InitPokeBoxs(ctx, logger, db)
	item.InitItems(ctx, logger, db)
	inventory.InitInventory(ctx, logger, db)
	poketeam.InitTeam(ctx, logger, db)
	market.InitSwop(ctx, logger, db)
	email.InitEmailsTable(ctx, logger, db)
	battle.InitBattleRecords(ctx, logger, db)
	trainer.InitTrainerCloth(ctx, logger, db)
	trainer.InitTrainerTitle(ctx, logger, db)
	quest.InitQuestSystem(ctx, logger, db, nk) // InitQuestSystem
	battlePlugin := battle.NewBattlePlugin()
	// newPokeMatch := NewPokeMatch()
	// 定义要注册的 Match 和 RPC 函数
	registrations := map[string]interface{}{
		battle.PokeStandardMatch: battle.NewPokeMatch, // Match 注册
		// "CreateCustomMatch":      battle.CreateCustomMatch, // RPC 注册
		"AaaaTest":                quest.AaaaTest, // RPC 测试函数
		"AAATestSelectTrainer":    admin.RpcAAATestSelectTrainer,
		"AAATestUseTrainerItem":   admin.RpcAAATestUseTrainerItem,
		"AAATestAcceptQuest":      admin.RpcAAATestAcceptQuest,
		"AAATestCompleteQuest":    admin.RpcAAATestCompleteQuest,
		"AAATestBuyStoreItem":     admin.RpcAAATestBuyStoreItem,
		"AAATestTryUseSummonItem": admin.RpcAAATestTryUseSummonItem,

		"RpcBattlePrepare":        battle.RpcBattlePrepare,
		"RpcMatchList":            battle.RpcMatchList,
		"BattleNpcResp":           battle.BattleNpcResp,
		"RpcBattleChoiceMesssage": battle.RpcBattleChoiceMesssage,
		"RpcInviteBattle":         battle.RpcInviteBattle,
		"RpcAcceptBattleInvite":   battle.RpcAcceptBattleInvite,
		"RpcGetBattleStatistics":  battle.RpcGetBattleStatistics,
		"RpcGetBattleCount":       battle.RpcGetBattleCount,
		// "RpcAddTrainerCloth":      trainer.RpcAddTrainerCloth,
		"RpcRemoveTrainerCloth": trainer.RpcRemoveTrainerCloth,
		"RpcAddTraienrTeam":     trainer.RpcAddTraienrTeam,
		"RpcGetTrainerBoxCloth": trainer.RpcGetTrainerBoxCloth,
		"RpcGetAllTrainerCloth": trainer.RpcGetAllTrainerCloth,
		"RpcUpdateTrainerCloth": trainer.RpcUpdateTrainerCloth,
		"RpcUseCapItem":         trainer.RpcUseCapItem,
		"RpcPokeChangeNature":   trainer.RpcPokeChangeNature,
		"RpcChangeMove":         trainer.RpcChangeMove,
		// "RpcPokeChangeAbility":  trainer.RpcPokeChangeAbility,
		"RpcPokeChangeEvs":    trainer.RpcPokeChangeEvs,
		"RpcLevelUpLearnMove": trainer.RpcLevelUpLearnMove,
		"RpcEvolveByLevel":    trainer.RpcEvolveByLevel,
		"RpcHatchEgg":         trainer.RpcHatchEgg,
		//派对
		"RpcGetPartyInfo": trainer.RpcGetPartyInfo,

		//任务
		"RpcAcceptQuest":                  trainer.RpcAcceptQuest,
		"RpcCompleteQuest":                trainer.RpcCompleteQuest,
		"RpcUpdateYarnTitleQuestProgress": trainer.RpcUpdateYarnTitleQuestProgress,
		"RpcGetActiveTrainerQuest":        trainer.RpcGetActiveTrainerQuest,
		// "RpcAddTrainerTitle":             trainer.RpcAddTrainerTitle,
		// "RpcRemoveTrainerTitle":          trainer.RpcRemoveTrainerTitle,
		"RpcGetAllTrainerTitles":         trainer.RpcGetAllTrainerTitles,
		"RpcGetUnavailableTrainerTitles": trainer.RpcGetUnavailableTrainerTitles,
		"RpcWearTrainerTitle":            trainer.RpcWearTrainerTitle,
		"RpcUnwearTrainerTitle":          trainer.RpcUnwearTrainerTitle,
		// "RpcGetTitleDailyQuota":          trainer.RpcGetTitleDailyQuota,
		"RegisterBattleServer":  battlePlugin.RegisterBattleServer,  // RPC 注册
		"UpdateClientResources": battlePlugin.UpdateClientResources, // RPC 注册
		// "GetMostIdleServer":      battlePlugin.GetMostIdleServer,     // RPC 注册
		// "RemoveClientFromCache":  battlePlugin.RemoveClientFromCache, // RPC 注册
		"SendMessageToServer": battlePlugin.SendMessageToServer,

		// "SavePokemonDistribution": poke.SavePokemonDistribution, // RPC 注册
		// "AppearPoke":              poke.TestRpcAppearPoke,       // RPC 注册
		"RpcQueryPokesByUpdateTs": poke.RpcQueryPokesByFilter,
		// "RpcQueryPokesByIds":      poke.RpcQueryPokesByIds,
		"RpcQueryPokesByFilter": poke.RpcQueryPokesByFilter,
		"RpcTestQueryPokes":     poke.RpcTestQueryPokes,
		"RpcUpdatePokeItem":     poke.RpcUpdatePokeItem,
		"RpcPokeInfo":           poke.RpcPokeInfo,
		// "PublicPokesMetadata": user.PublicPokesMetadata,
		"RpcTryUsePokeItemOne": trainer.RpcTryUsePokeItemOne,
		// "RpcTryUseItem":    trainer.RpcTryUseItem,
		"RpcTryUseTrainerItem": trainer.RpcTryUseTrainerItem,
		"RpcTryUsePokeItem":    trainer.RpcTryUsePokeItem,
		"RpcTryUseSummonItem":  trainer.RpcTryUseSummonItem,

		//Team
		"RpcTryUpTeamLevel":               trainer.RpcTryUpTeamLevel,
		"RpcDonateTeamByCoin":             trainer.RpcDonateTeamByCoin,
		"RpcConsumeTeamContributionToExp": trainer.RpcConsumeTeamContributionToExp,
		"RpcBuyStoreItem":                 trainer.RpcBuyStoreItem,

		// "RpcTraienrAllInventory":            trainer.RpcTraienrAllInventory,
		"TryTakePoke":                       trainer.TryTakePoke,
		"RpcGetTrainerAroundPokes":          trainer.RpcGetTrainerAroundPokes,
		"RpcGetTrainerBoxAndPokesByBoxInfo": trainer.RpcGetTrainerBoxAndPokesByBoxInfo,
		// select
		"RpcAllTrainer": trainer.RpcAllTrainer,
		// "TestRpcAddItem":      trainer.TestRpcAddItem,
		"RpcTrainerNameValid":   trainer.RpcTrainerNameValid,
		"RpcCreateTrainer":      trainer.RpcCreateTrainer,
		"RpcSelectTrainer":      trainer.RpcSelectTrainer,
		"RpcGetTrainerInfo":     trainer.RpcGetTrainerInfo,
		"RpcUpdateUserLoc":      trainer.RpcUpdateUserLoc,
		"RpcUpdateFollowPoke":   trainer.RpcUpdateFollowPoke,
		"RpcRecoverAroundPokes": trainer.RpcRecoverAroundPokes,
		// PokeBox
		"RpcTraienrAllPokeBox":    trainer.RpcTraienrAllPokeBox,
		"RpcBatchExchangePokeBox": trainer.RpcBatchExchangePokeBox,
		"RpcBreedPokemon":         trainer.RpcBreedPokemon,

		"RpcSalePokemon":   trainer.RpcSalePokemon,
		"RpcUnsalePokemon": trainer.RpcUnsalePokemon,

		"RpcSaleItem":    inventory.RpcSaleItem,
		"RpcUnsaleItem":  inventory.RpcUnsaleItem,
		"RpcQueryItems":  inventory.RpcQueryItems,
		"RpcGetAllItems": inventory.RpcGetAllItems,

		"UdpateBattleRole": user.UdpateBattleRole, // RPC 注册
		"UdpateDataRole":   user.UdpateDataRole,   // RPC 注册
		// "get_user_status_metadata":        getUserStatusMetadata,              // RPC 注册
		"RpcUpdateUserStatusMetadata": user.RpcUpdateUserStatusMetadata, // RPC 注册
		// "delete_user_status_field_metadata": deleteUserStatusFieldMetadata,      // RPC 注册

		// "QueryMarketPokesSql":        market.QueryMarketPokesSql,
		// "QueryMarketPokesCollection": market.QueryMarketPokesCollection,
		// "TestSalesRandomPoke":  market.TestSalesRandomPoke,
		"TestCreateRandomPoke": poke.TestCreateRandomPoke,

		"QueryItemsByFilter": item.QueryItemsByFilter,
		"QueryItemsByNames":  item.QueryItemsByNames,

		// "UpdateUserLoc": user.UpdateUserLoc,
		"RpcBuyItemsByKeyAndName": market.RpcBuyItemsByKeyAndName,
		// "RpcTransferPokemons":     market.RpcTransferPokemons,
		"RpcCreateSwopRequest": market.RpcCreateSwopRequest,
		"RpcAcceptSwopRequest": market.RpcAcceptSwopRequest,
		"RpcSubmitSwopContent": market.RpcSubmitSwopContent,
		"RpcConfirmSwop":       market.RpcConfirmSwop,
		"RpcCancelSubmitSwop":  market.RpcCancelSubmitSwop,
		"RpcCancelConfirmSwop": market.RpcCancelConfirmSwop,
		"RpcCloseSwop":         market.RpcCloseSwop,

		"RpcAdminSaveAllPokemonToTrainer": admin.RpcAdminSaveAllPokemonToTrainer,
		"RpcAdminSavePokemonToTrainer":    admin.RpcAdminSavePokemonToTrainer,
		"RpcSendEmailsToUsers":            admin.RpcSendEmailsToUsers,

		// Email system RPC functions
		"RpcGetEmailList":   trainer.RpcGetEmailList,
		"RpcEmailOperation": trainer.RpcEmailOperation,
	}

	// 遍历字典进行注册
	for name, handler := range registrations {
		switch h := handler.(type) {
		case func(context.Context, runtime.Logger, *sql.DB, runtime.NakamaModule, string) (string, error):
			// 注册 RPC 方法
			if err := initializer.RegisterRpc(name, h); err != nil {
				logger.Error("[RegisterRpc] error: %v", err)
				return err
			}
		case func(context.Context, runtime.Logger, *sql.DB, runtime.NakamaModule) (runtime.Match, error):
			// 注册 Match 方法
			if err := initializer.RegisterMatch(name, h); err != nil {
				logger.Error("[RegisterMatch] error: %v", err)
				return err
			}
		default:
			logger.Error("[Register] unknown handler type for: %s", name)
			return fmt.Errorf("unknown handler type for: %s", name)
		}
	}

	return nil
}

//********************************************************
// var globalData = map[string]string{}

// func SetGlobalInMemoryValue(key string, value string) {
//     globalData[key] = value
// }

// func GetGlobalInMemoryValue(key string) string {
//     return globalData[key]
// }
// const apiBaseUrl = "https://pokeapi.co/api/v2"

// // All Go modules must have a InitModule function with this exact signature.
// func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
// 	// Register the RPC function.
// 	if err := initializer.RegisterRpc("get_pokemon", GetPokemon); err != nil {
// 			logger.Error("Unable to register: %v", err)
// 			return err
// 	}
// 	return nil
// }

// func LookupPokemon(logger runtime.Logger, name string) {
// 	SetGlobalInMemoryValue("aaa", GetGlobalInMemoryValue("aaa")+"LookupPokemon");
// }

// func GetPokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	logger.Info("123456 aaaaa = %v", GetGlobalInMemoryValue("aaa"))
// 	LookupPokemon(logger, "PokemonName")
// 	return "{\"aaaaaa\":\"123456\"}", nil
// }
// import (
//     "context"
//     "database/sql"
//     "github.com/heroiclabs/nakama-common/runtime"
// // )
// import (
//     "context"
// 	"database/sql"
//     // "fmt"
//     "io"
//     "os/exec"
// 	"os"
//     // "bytes"
//     "strings"

//     "github.com/heroiclabs/nakama-common/runtime"
// )

// func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
//     logger.Info("Hello World!")
// 	    dir, err := os.Getwd()
//     if err != nil {
//         logger.Info("Error getting current directory: %v\n", err)
//         return nil
//     }
//     logger.Info("Current working directory:", dir)
// 	input := `>start {"formatid":"gen7randombattle"}
// >player p1 {"name":"Alice"}
// >player p2 {"name":"Bob"}
// `

//     // Create exec.Command object to run Pokémon Showdown
//     cmd := exec.Command("/pokemon-showdown/pokemon-showdown", "simulate-battle")

//     // Create pipes to pass input to the command
//     stdin, err := cmd.StdinPipe()
//     if err != nil {
//         logger.Info("Error creating stdin pipe: %v", err)
//         // return "", fmt.Errorf("Error creating stdin pipe: %v", err)
//     }

//     // Get standard output and standard error
//     stdout, err := cmd.StdoutPipe()
//     if err != nil {
//         logger.Info("Error creating stdout pipe: %v", err)
//         // return "", fmt.Errorf("Error creating stdout pipe: %v", err)
//     }
//     stderr, err := cmd.StderrPipe()
//     if err != nil {
//         logger.Info("Error creating stderr pipe: %v", err)
//         // return "", fmt.Errorf("Error creating stderr pipe: %v", err)
//     }

//     // Start the command
//     if err := cmd.Start(); err != nil {
//         logger.Info("Error starting command: %v", err)
//         // return "", fmt.Errorf("Error starting command: %v", err)
//     }

//     // Write data to stdin
//     _, err = stdin.Write([]byte(input))
//     if err != nil {
//         logger.Info("Error writing to stdin: %v", err)
//         // return "", fmt.Errorf("Error writing to stdin: %v", err)
//     }

//     // Close stdin
//     stdin.Close()

//     // Read command output
//     var output strings.Builder
//     if _, err = io.Copy(&output, stdout); err != nil {
//         logger.Info("Error reading from stdout: %v", err)
//         // return "", fmt.Errorf("Error reading from stdout: %v", err)
//     }

//     // Read error output
//     var errOutput strings.Builder
//     if _, err = io.Copy(&errOutput, stderr); err != nil {
//         logger.Info("Error reading from stderr: %v", err)
//         // return "", fmt.Errorf("Error reading from stderr: %v", err)
//     }

//     // Wait for the command to complete
//     if err := cmd.Wait(); err != nil {
//         logger.Info("Command execution failed: %v", err)
//         logger.Info("Command stderr output: %s", errOutput.String())
//         // return "", fmt.Errorf("Command execution failed: %v", err)
//     }

//     // Log the command output
//     logger.Info("Command output: %s", output.String())
//     return nil
// }
// package main

// import (
//     "context"
//     "fmt"
//     "io"
//     "os/exec"
//     "strings"

//     "github.com/heroiclabs/nakama-common/runtime"
// )

// // Plugin represents a Nakama plugin.
// type Plugin struct{}

// // NewPlugin creates a new instance of the Plugin.
// func NewPlugin() *Plugin {
//     return &Plugin{}
// }

// // MyCustomFunction handles a custom RPC call.
// func (p *Plugin) MyCustomFunction(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, payload string) (string, error) {
//     // Generate Pokémon Showdown input data
//     input := `>start {"formatid":"gen7randombattle"}
// >player p1 {"name":"Alice"}
// >player p2 {"name":"Bob"}
// `

//     // Create exec.Command object to run Pokémon Showdown
//     cmd := exec.Command("./pokemon-showdown", "simulate-battle")

//     // Create pipes to pass input to the command
//     stdin, err := cmd.StdinPipe()
//     if err != nil {
//         logger.Error("Error creating stdin pipe: %v", err)
//         return "", fmt.Errorf("Error creating stdin pipe: %v", err)
//     }

//     // Get standard output and standard error
//     stdout, err := cmd.StdoutPipe()
//     if err != nil {
//         logger.Error("Error creating stdout pipe: %v", err)
//         return "", fmt.Errorf("Error creating stdout pipe: %v", err)
//     }
//     stderr, err := cmd.StderrPipe()
//     if err != nil {
//         logger.Error("Error creating stderr pipe: %v", err)
//         return "", fmt.Errorf("Error creating stderr pipe: %v", err)
//     }

//     // Start the command
//     if err := cmd.Start(); err != nil {
//         logger.Error("Error starting command: %v", err)
//         return "", fmt.Errorf("Error starting command: %v", err)
//     }

//     // Write data to stdin
//     _, err = stdin.Write([]byte(input))
//     if err != nil {
//         logger.Error("Error writing to stdin: %v", err)
//         return "", fmt.Errorf("Error writing to stdin: %v", err)
//     }

//     // Close stdin
//     stdin.Close()

//     // Read command output
//     var output strings.Builder
//     if _, err = io.Copy(&output, stdout); err != nil {
//         logger.Error("Error reading from stdout: %v", err)
//         return "", fmt.Errorf("Error reading from stdout: %v", err)
//     }

//     // Read error output
//     var errOutput strings.Builder
//     if _, err = io.Copy(&errOutput, stderr); err != nil {
//         logger.Error("Error reading from stderr: %v", err)
//         return "", fmt.Errorf("Error reading from stderr: %v", err)
//     }

//     // Wait for the command to complete
//     if err := cmd.Wait(); err != nil {
//         logger.Error("Command execution failed: %v", err)
//         logger.Error("Command stderr output: %s", errOutput.String())
//         return "", fmt.Errorf("Command execution failed: %v", err)
//     }

//     // Log the command output
//     logger.Info("Command output: %s", output.String())

//     // Return the command output
//     return output.String(), nil
// }

// // InitModule initializes the Nakama module.
// func InitModule(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, initializer runtime.Initializer) error {
//     // Register the RPC function
//     err := initializer.RegisterRPC("my_custom_function", NewPlugin())
//     if err != nil {
//         logger.Error("Unable to register RPC: %v", err)
//         return err
//     }
//     return nil
// }
// Register the plugin
// func Register(module runtime.NakamaModule) {
//     module.RegisterRPC("my_custom_function", NewPlugin())
// }

// package main

// import (
//     "fmt"
//     "os/exec"
//     "os"
//     "bytes"
// )

// func main() {
//     dir, err := os.Getwd()
//     if err != nil {
//         fmt.Printf("Error getting current directory: %v\n", err)
//         return
//     }
//     fmt.Println("Current working directory:", dir)

//     // 定义要发送给 pokemon-showdown 的数据
//     input := `>start {"formatid":"gen7randombattle"}
// >player p1 {"name":"Alice"}
// >player p2 {"name":"Bob"}
// `

//     // 创建 exec.Command 对象来运行 pokemon-showdown
//     cmd := exec.Command("../../pokemon-showdown/pokemon-showdown", "simulate-battle")

//     // 创建管道来将输入传递到命令
//     stdin, err := cmd.StdinPipe()
//     if err != nil {
//         fmt.Printf("Error creating stdin pipe: %v\n", err)
//         return
//     }

//     // 捕获标准输出和标准错误
//     var outBuf, errBuf bytes.Buffer
//     cmd.Stdout = &outBuf
//     cmd.Stderr = &errBuf

//     // 启动命令
//     if err := cmd.Start(); err != nil {
//         fmt.Printf("Error starting command: %v\n", err)
//         return
//     }

//     // 将数据写入 stdin
//     _, err = stdin.Write([]byte(input))
//     if err != nil {
//         fmt.Printf("Error writing to stdin: %v\n", err)
//         return
//     }

//     // 关闭 stdin
//     stdin.Close()

//     // 等待命令完成
//     if err := cmd.Wait(); err != nil {
//         fmt.Printf("Command execution failed: %v\n", err)
//         fmt.Printf("Standard error output: %s\n", errBuf.String())
//         return
//     }

//     // 打印标准输出和错误输出
//     fmt.Printf("Command output: %s\n", outBuf.String())
//     fmt.Printf("Command executed successfully\n")
// }
