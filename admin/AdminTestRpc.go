package admin

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"go-nakama-poke/trainer"
	"go-nakama-poke/user"
	"strconv"

	"github.com/heroiclabs/nakama-common/runtime"
)

func RpcAAATestSelectTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	tid, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}

	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}
	trainerInfo, err := trainer.SelectTrainerProto(ctx, db, tid)
	// if err != nil || trainer.Uid != userID {
	// 	return "", runtime.NewError("not found tid in uid", 400)
	// }
	tool.SetUserActiveTrainer(userID, trainerInfo)
	return "{}", nil
}
func RpcAAATestAcceptQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}
	return trainer.RpcAcceptQuest(ctx, logger, db, nk, payload)
}
func RpcAAATestCompleteQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}
	return trainer.RpcCompleteQuest(ctx, logger, db, nk, payload)
}
func RpcAAATestUseTrainerItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}
	// return trainer.RpcTryUseTrainerItem(ctx, logger, db, nk, payload)
	var itemPayload = &MainServer.UseItemInfo{}
	itemPayload.ItemName = payload
	itemPayload.Quantity = 1
	trainerInfo := tool.GetActiveTrainerByCtx(ctx)
	if trainerInfo == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", runtime.NewError("Not found tid", 404)
	}
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	response, err := trainer.TryUseTrainerItem(ctx, logger, tx, nk, trainerInfo, itemPayload)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToJson(response)
}
func RpcAAATestBuyStoreItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}
	var itemPayload = &MainServer.RpcBuyItemRequest{}
	itemPayload.BuySiteType = MainServer.BuySiteType_BuySiteTypeTeamStore
	itemPayload.InventoryType = MainServer.InventoryType_inventory_summon
	itemPayload.ItemName = "summon_level_3"
	itemPayload.Count = 100
	payload, err = tool.ProtoToBase64(itemPayload)
	if err != nil {
		return "", fmt.Errorf("failed to convert itemPayload to base64: %w", err)
	}
	return trainer.RpcBuyStoreItem(ctx, logger, db, nk, payload)
}

func RpcAAATestTryUseSummonItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}
	var itemPayload = &MainServer.UseSummonItemInfo{}
	itemPayload.ItemId = 1
	itemPayload.ItemName = "summon_level_3"
	itemPayload.Quantity = 1
	// if err := tool.Base64ToProto(payload, itemPayload); err != nil {
	// 	logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
	// 	return "", runtime.NewError("无效的请求数据", 400)
	// }
	trainerInfo := tool.GetActiveTrainerByCtx(ctx)
	if trainerInfo == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", runtime.NewError("Not found tid", 404)
	}
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	response, err := trainer.TryUseSummonItem(ctx, logger, tx, nk, trainerInfo, itemPayload)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToJson(response)
}
